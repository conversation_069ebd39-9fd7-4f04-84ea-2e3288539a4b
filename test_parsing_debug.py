#!/usr/bin/env python3
# coding: utf-8
"""
调试预警内容解析功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import re
from typing import List, Dict, Optional

def extract_admin_region_name(alarm_content: str) -> str:
    """提取行政区名称（气象台之前的内容）"""
    match = re.search(r'^(.+?)气象台', alarm_content)
    if match:
        return match.group(1).strip()
    return ""

def extract_content_after_colon(alarm_content: str) -> str:
    """提取第一个冒号后的内容"""
    # 先查找中文冒号
    chinese_colon_index = alarm_content.find('：')
    # 再查找英文冒号
    english_colon_index = alarm_content.find(':')

    # 选择最早出现的冒号
    colon_index = -1
    if chinese_colon_index != -1 and english_colon_index != -1:
        colon_index = min(chinese_colon_index, english_colon_index)
    elif chinese_colon_index != -1:
        colon_index = chinese_colon_index
    elif english_colon_index != -1:
        colon_index = english_colon_index

    if colon_index != -1:
        result = alarm_content[colon_index + 1:].strip()
        print(f"找到冒号位置: {colon_index}, 冒号后内容: '{result}'")
        return result
    return ""

def extract_time_and_location(content: str) -> str:
    """提取时间信息后的地点内容"""
    print(f"输入内容: {content}")
    
    # 简化的匹配策略
    # 1. 先找到"天"或"小时"的位置
    time_patterns = [r'未来\d+(?:天|小时)', r'\d+(?:天|小时)', r'(?:天|小时)']

    for time_pattern in time_patterns:
        match = re.search(time_pattern, content)
        if match:
            print(f"找到时间模式: {match.group()} 在位置 {match.start()}-{match.end()}")
            # 获取时间词后的内容
            after_time = content[match.end():].strip()
            print(f"时间后的内容: {after_time}")

            # 去除开头的标点符号和空格
            after_time = re.sub(r'^[，,。！？\s]+', '', after_time)
            print(f"清理后的内容: {after_time}")

            # 查找地点信息的策略：
            # 1. 优先匹配包含多个地名的完整列表（如：县名+多个乡镇名）
            # 2. 匹配单个地名
            # 3. 匹配第一个逗号前的内容
            location_patterns = [
                # 匹配县/市名 + 多个乡镇名的模式（用、或，分隔）
                r'([^，,。！？]*?(?:县|市|区)[^，,。！？]*?(?:[、，,][^，,。！？]*?(?:镇|乡|街道|村|等))+[^，,。！？]*?)(?:[，,。！？]|$)',
                # 匹配包含地名标识的单个地点
                r'([^，,。！？]*?(?:县|市|镇|乡|区|街道|村|等)[^，,。！？]*?)(?:[，,。！？]|$)',
                # 匹配第一个逗号前的内容
                r'([^，,。！？]+?)(?:[，,。！？]|$)'
            ]

            for i, loc_pattern in enumerate(location_patterns):
                print(f"尝试地点模式 {i+1}: {loc_pattern}")
                loc_match = re.search(loc_pattern, after_time)
                if loc_match:
                    location_text = loc_match.group(1).strip()
                    print(f"匹配到地点: {location_text}")
                    if location_text:
                        return location_text
                else:
                    print(f"模式 {i+1} 未匹配")

            break
        else:
            print(f"时间模式 {time_pattern} 未匹配")

    return ""

def clean_location_prefix(location: str, admin_region: str) -> str:
    """清理地点前缀"""
    print(f"清理前: {location}, 行政区: {admin_region}")

    # 去除常见前缀
    prefixes = ['我县', '我区', '我州', '我市']
    for prefix in prefixes:
        if location.startswith(prefix):
            location = location[len(prefix):].strip()
            print(f"去除前缀 {prefix}: {location}")
            break

    # 去除行政区名称前缀
    if admin_region and location.startswith(admin_region):
        location = location[len(admin_region):].strip()
        print(f"去除行政区前缀: {location}")

    # 去除末尾的多余文字（如"将出现冰雹"、"等乡镇"等）
    suffixes_to_remove = [
        r'等乡镇.*$',  # 去除"等乡镇"及其后面的所有内容
        r'将出现.*$',  # 去除"将出现"及其后面的所有内容
        r'等地.*$',    # 去除"等地"及其后面的所有内容
        r'等.*$'       # 去除"等"及其后面的所有内容（最后匹配，避免过度匹配）
    ]

    for suffix_pattern in suffixes_to_remove:
        new_location = re.sub(suffix_pattern, '', location).strip()
        if new_location != location:
            print(f"去除后缀 '{suffix_pattern}': {location} -> {new_location}")
            location = new_location
            break

    print(f"清理后: {location}")
    return location

def split_locations(location_text: str) -> List[str]:
    """使用顿号分割地点"""
    print(f"分割地点: {location_text}")
    # 使用顿号分割
    locations = re.split(r'[、]', location_text)
    result = [loc.strip() for loc in locations if loc.strip()]
    print(f"分割结果: {result}")
    return result

def debug_parse_alarm_content():
    """调试解析预警内容"""
    alarm_content = "武定县气象台2025年7月27日16时00分发布冰雹橙色预警信号:预计未来6小时武定县狮山、高桥、猫街、白路、环州、插甸、田心、东坡、发窝、万德、己衣等乡镇将出现冰雹，请做好防范。（预警信息来源：国家预警信息发布中心）"
    
    print("=" * 80)
    print("开始解析预警内容")
    print(f"原始内容: {alarm_content}")
    print("=" * 80)
    
    # 1. 提取行政区名称
    print("\n步骤1: 提取行政区名称")
    admin_region = extract_admin_region_name(alarm_content)
    print(f"行政区名称: '{admin_region}'")
    
    # 2. 提取冒号后的内容
    print("\n步骤2: 提取冒号后的内容")
    content_after_colon = extract_content_after_colon(alarm_content)
    print(f"冒号后内容: '{content_after_colon}'")
    if not content_after_colon:
        print("❌ 步骤2失败：未找到冒号后的内容")
        return
    
    # 3. 提取时间后的地点内容
    print("\n步骤3: 提取时间后的地点内容")
    location_text = extract_time_and_location(content_after_colon)
    print(f"地点内容: '{location_text}'")
    if not location_text:
        print("❌ 步骤3失败：未找到地点内容")
        return
    
    # 4. 清理地点前缀
    print("\n步骤4: 清理地点前缀")
    cleaned_location = clean_location_prefix(location_text, admin_region)
    print(f"清理后地点: '{cleaned_location}'")
    if not cleaned_location:
        print("❌ 步骤4失败：清理后地点为空")
        return
    
    # 5. 分割地点
    print("\n步骤5: 分割地点")
    locations = split_locations(cleaned_location)
    print(f"分割后地点列表: {locations}")
    if not locations:
        print("❌ 步骤5失败：分割后地点列表为空")
        return
    
    print("\n✅ 解析成功完成！")
    print(f"最终结果: {len(locations)} 个地点")
    for i, loc in enumerate(locations, 1):
        print(f"  {i}. {loc}")

if __name__ == "__main__":
    debug_parse_alarm_content()
