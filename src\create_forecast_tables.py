#!/usr/bin/env python3
# coding: utf-8
"""
创建降水预报相关数据表
包括：
- weather_cell_6m: 天气格网降水数据表（6分钟数据）
- forecast_precipitation_6min_line: 6分钟降水预报线数据表
- forecast_precipitation_6min_polygon: 6分钟降水预报面数据表
- forecast_precipitation_6min_relation: 6分钟降水预报关系表
- forecast_precipitation_hourly_line: 1小时降水预报线数据表
- forecast_precipitation_hourly_polygon: 1小时降水预报面数据表
- forecast_precipitation_hourly_relation: 1小时降水预报关系表
- forecast_precipitation_summary_line: 日降水预报线数据表
- forecast_precipitation_summary_polygon: 日降水预报面数据表
- forecast_precipitation_summary_relation: 日降水预报关系表
- sp_precip_polygon: 通用降水面数据处理存储过程
- sp_precip_line: 通用降水线数据处理存储过程
"""

from sqlalchemy import create_engine, text

# 导入统一配置
from config import PG_URL

def create_weather_cell_6m_table():
    """
    创建 weather_cell_6m 表（如果不存在）- 移除type字段，由动态函数计算
    包含完整的字段注释和表注释
    按照分区改造方案A：使用(pre_time, cell_id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
/*==============================================================
 *  6 分钟天气格网数据表  weather_cell_6m (分区表)
 *=============================================================*/
CREATE TABLE IF NOT EXISTS public.weather_cell_6m (
    id        BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,  -- 不再是主键，保留作为行标识符
    pre_time  timestamp      NOT NULL,     -- 分区键，必须非空
    cell_id   integer        NOT NULL,
    rainfall  numeric(8,3)   NOT NULL,
    phase     integer        NOT NULL,
    -- 新的复合主键：分区键 + 原始唯一标识
    CONSTRAINT weather_cell_6m_pkey PRIMARY KEY (pre_time, cell_id)
) PARTITION BY RANGE (pre_time);

/*--------------------------------------------------------------
 * 索引
 *-------------------------------------------------------------*/
-- 1. 原来的唯一索引不再需要，因为主键已经保证了(pre_time, cell_id)的唯一性
-- CREATE UNIQUE INDEX IF NOT EXISTS uq_weather_cell_6m_time_cell_inc
--     ON public.weather_cell_6m (pre_time, cell_id)
--     INCLUDE (rainfall, phase);

-- 2. 供 sp_precip_line
--    SELECT DISTINCT ON (cell_id) ORDER BY rainfall DESC, pre_time DESC
--    的语句使用，完全覆盖，无需回表
CREATE INDEX IF NOT EXISTS idx_weather_cell_6m_cell_rain_time_desc
    ON public.weather_cell_6m (cell_id,
                               rainfall DESC,
                               pre_time DESC)
    INCLUDE (phase);

-- 3. 供“时间范围 + DISTINCT”快速过滤，
--    把 pre_time 放在第一列，可高效利用
CREATE INDEX IF NOT EXISTS idx_weather_cell_6m_time_cell_rain_desc
    ON public.weather_cell_6m (pre_time,
                               cell_id,
                               rainfall DESC)
    INCLUDE (phase);

-- 4. “保底”单列索引 —— 留给可能的 ad-hoc 查询
CREATE INDEX IF NOT EXISTS idx_weather_cell_6m_cell_id
    ON public.weather_cell_6m (cell_id);

-- 5. 历史大范围查询走 BRIN
CREATE INDEX IF NOT EXISTS brin_weather_cell_6m_pre_time
    ON public.weather_cell_6m
 USING brin (pre_time)
 WITH (pages_per_range = 128);

ALTER TABLE public.weather_cell_6m OWNER TO root;
    """
    return create_table_sql

def create_weather_cell_1h_table() -> str:
    """
    返回创建 / 更新 weather_cell_1h 表及其索引的 SQL
    按照分区改造方案A：使用(pre_time, cell_id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
/*==============================================================
 *  1 小时天气格网数据表  weather_cell_1h (分区表)
 *=============================================================*/
CREATE TABLE IF NOT EXISTS public.weather_cell_1h (
    id        BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,  -- 不再是主键，保留作为行标识符
    pre_time  timestamp      NOT NULL,     -- 分区键，必须非空
    cell_id   integer        NOT NULL,
    pre       numeric(8,3),
    weather   integer,
    vis       integer,
    tem       numeric(8,3),
    -- 新的复合主键：分区键 + 原始唯一标识
    CONSTRAINT weather_cell_1h_pkey PRIMARY KEY (pre_time, cell_id)
) PARTITION BY RANGE (pre_time);

/*--------------------------------------------------------------
 * 索引
 *-------------------------------------------------------------*/
-- 1. 原来的唯一索引不再需要，因为主键已经保证了(pre_time, cell_id)的唯一性
-- CREATE UNIQUE INDEX IF NOT EXISTS uq_weather_cell_1h_time_cell_inc
--     ON public.weather_cell_1h (pre_time, cell_id)
--     INCLUDE (pre, weather, vis, tem);

-- 2. 供 sp_precip_polygon
--    SELECT DISTINCT ON (cell_id) ORDER BY pre DESC, pre_time DESC
--    的语句使用，字段全部覆盖
CREATE INDEX IF NOT EXISTS idx_weather_cell_1h_cell_pre_time_desc
    ON public.weather_cell_1h (cell_id,
                               pre DESC,
                               pre_time DESC)
    INCLUDE (weather, vis, tem);

-- 3. 按时间范围过滤 + DISTINCT 时使用
CREATE INDEX IF NOT EXISTS idx_weather_cell_1h_time_cell_pre_desc
    ON public.weather_cell_1h (pre_time,
                               cell_id,
                               pre DESC)
    INCLUDE (weather, vis, tem);

-- 4. “保底”单列索引 —— 继续保留
CREATE INDEX IF NOT EXISTS idx_weather_cell_1h_cell_id
    ON public.weather_cell_1h (cell_id);

-- 5. BRIN：大范围历史查询
CREATE INDEX IF NOT EXISTS brin_weather_cell_1h_pre_time
    ON public.weather_cell_1h
 USING brin (pre_time)
 WITH (pages_per_range = 128);

ALTER TABLE public.weather_cell_1h OWNER TO root;
    """
    return create_table_sql


def create_weather_cell_obs_table() -> str:
    """
    创建实况天气格网数据表 weather_cell_obs
    包含所有实况字段：PRE10m、PRE1h、TEM、VIS、WEATHER、PRS、PRSSea、RHU、U、V、WIND、WINS
    按照分区改造方案A：使用(pre_time, cell_id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
/*==============================================================
 *  实况天气格网数据表  weather_cell_obs (分区表)
 *=============================================================*/
CREATE TABLE IF NOT EXISTS public.weather_cell_obs (
    id        BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,  -- 不再是主键，保留作为行标识符
    pre_time  timestamp      NOT NULL,     -- 分区键，必须非空，存储time或reftime
    cell_id   integer        NOT NULL,
    pre10m    numeric(8,3),               -- 10分钟降水（单位：毫米）
    pre1h     numeric(8,3),               -- 最近一小时降水（单位：毫米）
    prs       numeric(8,2),               -- 气压（单位：百帕）
    prs_sea   numeric(8,2),               -- 海洋气压（单位：百帕）
    rhu       numeric(5,2),               -- 湿度（单位：百分比）
    tem       numeric(8,3),               -- 温度（单位：摄氏度）
    u         numeric(8,3),               -- 气象风场U分量（单位：米/秒）
    v         numeric(8,3),               -- 气象风场V分量（单位：米/秒）
    vis       integer,                    -- 能见度（单位：千米）
    weather   integer,                    -- 天气现象编码
    wind      integer,                    -- 风向编码
    wins      integer,                    -- 风力编码
    -- 新的复合主键：分区键 + 原始唯一标识
    CONSTRAINT weather_cell_obs_pkey PRIMARY KEY (pre_time, cell_id)
) PARTITION BY RANGE (pre_time);

/*--------------------------------------------------------------
 * 索引
 *-------------------------------------------------------------*/
-- 1. 供 sp_precip_polygon 使用 PRE10m 数据
--    SELECT DISTINCT ON (cell_id) ORDER BY pre10m DESC, pre_time DESC
CREATE INDEX IF NOT EXISTS idx_weather_cell_obs_cell_pre10m_time_desc
    ON public.weather_cell_obs (cell_id,
                                pre10m DESC,
                                pre_time DESC)
    INCLUDE (weather, vis, tem);

-- 2. 供 sp_precip_polygon 使用 PRE1h 数据
--    SELECT DISTINCT ON (cell_id) ORDER BY pre1h DESC, pre_time DESC
CREATE INDEX IF NOT EXISTS idx_weather_cell_obs_cell_pre1h_time_desc
    ON public.weather_cell_obs (cell_id,
                                pre1h DESC,
                                pre_time DESC)
    INCLUDE (weather, vis, tem);

-- 3. 按时间范围过滤 + DISTINCT 时使用 PRE10m
CREATE INDEX IF NOT EXISTS idx_weather_cell_obs_time_cell_pre10m_desc
    ON public.weather_cell_obs (pre_time,
                                cell_id,
                                pre10m DESC)
    INCLUDE (weather, vis, tem);

-- 4. 按时间范围过滤 + DISTINCT 时使用 PRE1h
CREATE INDEX IF NOT EXISTS idx_weather_cell_obs_time_cell_pre1h_desc
    ON public.weather_cell_obs (pre_time,
                                cell_id,
                                pre1h DESC)
    INCLUDE (weather, vis, tem);

-- 5. "保底"单列索引
CREATE INDEX IF NOT EXISTS idx_weather_cell_obs_cell_id
    ON public.weather_cell_obs (cell_id);

-- 6. BRIN：大范围历史查询
CREATE INDEX IF NOT EXISTS brin_weather_cell_obs_pre_time
    ON public.weather_cell_obs
 USING brin (pre_time)
 WITH (pages_per_range = 128);

ALTER TABLE public.weather_cell_obs OWNER TO root;
    """
    return create_table_sql


def create_get_rainfall_type_function():
    """
    创建 get_rainfall_type PostgreSQL 函数
    """
    function_sql = """
    CREATE OR REPLACE FUNCTION get_rainfall_type(rainfall numeric, phase int)
    RETURNS int
    LANGUAGE plpgsql
    IMMUTABLE
    AS $$
    BEGIN
        -- 无降水
        IF rainfall <= 0 THEN
            RETURN 0;
        END IF;

        -- 雨
        IF phase = 1 THEN
            IF rainfall >= 10 THEN
                RETURN 6;  -- 特大暴雨
            ELSIF rainfall >= 4 THEN
                RETURN 5;  -- 大暴雨
            ELSIF rainfall >= 1.2 THEN
                RETURN 4;  -- 暴雨
            ELSIF rainfall >= 0.5 THEN
                RETURN 3;  -- 大雨
            ELSIF rainfall >= 0.2 THEN
                RETURN 2;  -- 中雨
            ELSIF rainfall > 0 THEN
                RETURN 1;  -- 小雨
            END IF;
        -- 雪
        ELSIF phase = 3 THEN
            IF rainfall >= 1 THEN
                RETURN 15; -- 大暴雪
            ELSIF rainfall >= 0.3 THEN
                RETURN 14; -- 暴雪
            ELSIF rainfall >= 0.13 THEN
                RETURN 13; -- 大雪
            ELSIF rainfall >= 0.04 THEN
                RETURN 12; -- 中雪
            ELSIF rainfall > 0 THEN
                RETURN 11; -- 小雪
            END IF;
        END IF;

        -- 其他情况返回无降水
        RETURN 0;
    END;
    $$;

    COMMENT ON FUNCTION get_rainfall_type(numeric, int) IS '根据降雨量和相态返回降水类型编码';
    """
    return function_sql


def create_get_rainfall_type_hourly_function():
    """
    创建 get_rainfall_type_hourly PostgreSQL 函数
    用于1小时降水数据，只有pre参数，值都要大10倍
    """
    function_sql = """
    CREATE OR REPLACE FUNCTION get_rainfall_type_hourly(pre numeric)
    RETURNS int
    LANGUAGE plpgsql
    IMMUTABLE
    AS $$
    BEGIN
        -- 无降水
        IF pre <= 0 THEN
            RETURN 0;
        END IF;

        -- 雨
        IF pre >= 45 THEN
            RETURN 6;  -- 特大暴雨
        ELSIF pre >= 20 THEN
            RETURN 5;  -- 大暴雨
        ELSIF pre >= 8 THEN
            RETURN 4;  -- 暴雨
        ELSIF pre >= 4 THEN
            RETURN 3;  -- 大雨
        ELSIF pre >= 2 THEN
            RETURN 2;  -- 中雨
        ELSIF pre > 0 THEN
            RETURN 1;  -- 小雨
        END IF;

        -- 其他情况返回无降水
        RETURN 0;
    END;
    $$;

    COMMENT ON FUNCTION get_rainfall_type_hourly(numeric) IS '根据1小时降水量返回降水类型编码';
    """
    return function_sql

def create_get_rainfall_type_obs_10m_function():
    """
    创建 get_rainfall_type_obs_10m PostgreSQL 函数
    用于实况10分钟降水数据，阈值与6分钟数据相同
    """
    function_sql = """
    CREATE OR REPLACE FUNCTION get_rainfall_type_obs_10m(pre10m numeric)
    RETURNS int
    LANGUAGE plpgsql
    IMMUTABLE
    AS $$
    BEGIN
        -- 无降水
        IF pre10m <= 0 THEN
            RETURN 0;
        END IF;

        -- 雨（阈值与6分钟数据相同）
        IF pre10m >= 15 THEN
            RETURN 6;  -- 特大暴雨
        ELSIF pre10m >= 6 THEN
            RETURN 5;  -- 大暴雨
        ELSIF pre10m >= 2 THEN
            RETURN 4;  -- 暴雨
        ELSIF pre10m >= 0.8 THEN
            RETURN 3;  -- 大雨
        ELSIF pre10m >= 0.3 THEN
            RETURN 2;  -- 中雨
        ELSIF pre10m > 0 THEN
            RETURN 1;  -- 小雨
        END IF;

        -- 其他情况返回无降水
        RETURN 0;
    END;
    $$;

    COMMENT ON FUNCTION get_rainfall_type_obs_10m(numeric) IS '根据实况10分钟降水量返回降水类型编码';
    """
    return function_sql


def create_get_rainfall_type_obs_1h_function():
    """
    创建 get_rainfall_type_obs_1h PostgreSQL 函数
    用于实况1小时降水数据，阈值与预报1小时数据相同
    """
    function_sql = """
    CREATE OR REPLACE FUNCTION get_rainfall_type_obs_1h(pre1h numeric)
    RETURNS int
    LANGUAGE plpgsql
    IMMUTABLE
    AS $$
    BEGIN
        -- 无降水
        IF pre1h <= 0 THEN
            RETURN 0;
        END IF;

        -- 雨（阈值与预报1小时数据相同）
        IF pre1h >= 45 THEN
            RETURN 6;  -- 特大暴雨
        ELSIF pre1h >= 20 THEN
            RETURN 5;  -- 大暴雨
        ELSIF pre1h >= 8 THEN
            RETURN 4;  -- 暴雨
        ELSIF pre1h >= 4 THEN
            RETURN 3;  -- 大雨
        ELSIF pre1h >= 2 THEN
            RETURN 2;  -- 中雨
        ELSIF pre1h > 0 THEN
            RETURN 1;  -- 小雨
        END IF;

        -- 其他情况返回无降水
        RETURN 0;
    END;
    $$;

    COMMENT ON FUNCTION get_rainfall_type_obs_1h(numeric) IS '根据实况1小时降水量返回降水类型编码，阈值与预报1小时数据相同';
    """
    return function_sql


def create_sp_precip_polygon_procedure() -> str:
    """
    创建通用的 sp_precip_polygon  存储过程
    ──────────────────────────────────────────────────────────
    1) 允许指定：
       • 计算类型的函数(p_calc_func)  – 默认 get_rainfall_type
       • 传给该函数的字段(p_arg_fields)
       • 数据来源表(p_source_table)
       • 要取的值字段(p_value_field)
       • 目标 Polygon 表(p_target_polygon)
       • 目标 Relation 表(p_target_relation)
       • 起止时间(单点或时间段)
    2) 内部流程：
       0. 删除旧 Polygon 记录 (Relation 通过外键联动删除)
       1. 计算 Polygon，并临时建立 "type-cid ↔ polygon_id" 映射
       2. 处理路段切片聚合，生成 relation 记录
    """
    procedure_sql = """
/*====================================================================
 *  名称 :  sp_precip_polygon  –  “区间并集 + 子串切割（不合并）”  PG-16 版
 *  说明 : ① 只要桩号出现空档就生成新的 LineString
 *         ② 每条结果线都保留自身的 pk_from / pk_to
 *         ③ 不再做 ST_Collect / ST_LineMerge
 *         ④ relation 表新增 region_code / region_name
 *-------------------------------------------------------------------
 *  主要思路
 *    1. 先把属于同一雨区的桩号段做 multirange(range_agg) 并集，
 *       得到真正“连续”的数值区间；
 *    2. 把每个区间和 weather_routes 逐条记录做桩号重叠判断，
 *       用 ST_LineSubstring 直接截母线；
 *    3. 交集桩号 = GREATEST / LEAST，保证每条子线自己的
 *       pile_start / pile_end 正确；
 *    4. clip_seg 结果就是最终结果——一行一段，不再做 Merge。
 *===================================================================*/

DROP PROCEDURE IF EXISTS sp_precip_polygon (
      timestamp, timestamp,
      text, text[], text,
      text, text, text );

CREATE OR REPLACE PROCEDURE sp_precip_polygon (
    IN  p_time            timestamp,                     -- 起报时刻
    IN  p_time_e          timestamp DEFAULT NULL,        -- 结束时刻
    IN  p_calc_func       text      DEFAULT 'get_rainfall_type_hourly',
    IN  p_arg_fields      text[]    DEFAULT ARRAY['pre'],
    IN  p_source_table    text      DEFAULT 'weather_cell_1h',
    IN  p_value_field     text      DEFAULT 'pre',
    IN  p_target_polygon  text      DEFAULT 'forecast_precipitation_hourly_polygon',
    IN  p_target_relation text      DEFAULT 'forecast_precipitation_hourly_relation'
)
LANGUAGE plpgsql
AS $BODY$
DECLARE
/*------------------------------------------------------------
 * 一、变量
 *-----------------------------------------------------------*/
    /*—— 解析出的 schema / table ——*/
    src_schema  text;  src_table  text;
    poly_schema text;  poly_table text;
    rel_schema  text;  rel_table  text;

    /*—— weather_routes 固定表 ——*/
    route_schema text := 'public';
    route_table  text := 'weather_routes';

    /*—— quote 之后的完全限定名 ——*/
    q_calc_func       text;
    q_source_table    text;
    q_target_polygon  text;
    q_target_relation text;
    q_route_table     text;
    q_value_field     text;

    /*—— 运行期中间变量 ——*/
    field_list        text;
    sql               text;
    rel_columns       text;
    need_poly_dur     boolean := false;
    need_rel_dur      boolean := false;
    del_poly_cond     text;
    del_rel_cond      text;
    raw_time_cond     text;
BEGIN
/*------------------------------------------------------------
 * 二、会话级配置
 *-----------------------------------------------------------*/
    PERFORM set_config('work_mem','512MB',true);
    PERFORM set_config('synchronous_commit','off',true);
    SET CONSTRAINTS ALL DEFERRED;

/*------------------------------------------------------------
 * 三、schema / table 解析 & quote_ident
 *-----------------------------------------------------------*/
    /* 1. 源数据表 */
    IF strpos(p_source_table,'.')>0 THEN
        src_schema := split_part(p_source_table,'.',1);
        src_table  := split_part(p_source_table,'.',2);
    ELSE
        src_schema := current_schema();
        src_table  := p_source_table;
    END IF;
    q_source_table := quote_ident(src_schema)||'.'||quote_ident(src_table);

    /* 2. 目标 polygon 表 */
    IF strpos(p_target_polygon,'.')>0 THEN
        poly_schema := split_part(p_target_polygon,'.',1);
        poly_table  := split_part(p_target_polygon,'.',2);
    ELSE
        poly_schema := current_schema();
        poly_table  := p_target_polygon;
    END IF;
    q_target_polygon := quote_ident(poly_schema)||'.'||quote_ident(poly_table);

    /* 3. 目标 relation 表 */
    IF strpos(p_target_relation,'.')>0 THEN
        rel_schema := split_part(p_target_relation,'.',1);
        rel_table  := split_part(p_target_relation,'.',2);
    ELSE
        rel_schema := current_schema();
        rel_table  := p_target_relation;
    END IF;
    q_target_relation := quote_ident(rel_schema)||'.'||quote_ident(rel_table);

    /* 4. weather_routes */
    q_route_table := quote_ident(route_schema)||'.'||quote_ident(route_table);

    /* 5. 计算函数 */
    IF strpos(p_calc_func,'.')>0 THEN
        q_calc_func := quote_ident(split_part(p_calc_func,'.',1))
                    ||'.'||quote_ident(split_part(p_calc_func,'.',2));
    ELSE
        q_calc_func := quote_ident(p_calc_func);
    END IF;

    q_value_field := quote_ident(p_value_field);

/*------------------------------------------------------------
 * 四、动态字段清单 r.field1, r.field2, …
 *-----------------------------------------------------------*/
    SELECT string_agg('r.'||quote_ident(f),', ')
      INTO field_list
    FROM unnest(p_arg_fields) AS f;

/*------------------------------------------------------------
 * 五、forecast_duration 字段判断
 *-----------------------------------------------------------*/
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
         WHERE table_schema = poly_schema
           AND table_name   = poly_table
           AND column_name  = 'forecast_duration')
      INTO need_poly_dur;

    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
         WHERE table_schema = rel_schema
           AND table_name   = rel_table
           AND column_name  = 'forecast_duration')
      INTO need_rel_dur;

/*------------------------------------------------------------
 * 六、删除旧数据过滤条件
 *-----------------------------------------------------------*/
    del_poly_cond :=
        CASE WHEN need_poly_dur THEN
            'AND ( $2 IS NULL OR forecast_duration = FLOOR(EXTRACT(EPOCH FROM ($2-$1))/3600 + 1)::int )'
        ELSE '' END;

    del_rel_cond :=
        CASE WHEN need_rel_dur THEN
            'AND ( $2 IS NULL OR forecast_duration = FLOOR(EXTRACT(EPOCH FROM ($2-$1))/3600 + 1)::int )'
        ELSE '' END;

/*------------------------------------------------------------
 * 七、tmp_raw 时间过滤
 *-----------------------------------------------------------*/
    raw_time_cond :=
        CASE WHEN p_time_e IS NULL
             THEN 'r.pre_time = $1'
             ELSE 'r.pre_time >= $1 AND r.pre_time < $2'
        END;

/*------------------------------------------------------------
 * 八、relation 列清单 （新增 region_code / region_name）
 *-----------------------------------------------------------*/
    rel_columns := 'road_id, road_num, road_name,'||
                   'pile_start, pile_end, section_length,'||
                   'management_office, maintenance_section, owner_type,'||
                   'management_office_id, maintenance_section_id,'||
                   'region_code, region_name,'||
                   'center, shape, polygon_id, pre_time'||
                   CASE WHEN need_rel_dur THEN ', forecast_duration' ELSE '' END;

/*------------------------------------------------------------
 * 九、主体动态 SQL
 *-----------------------------------------------------------*/
    sql := format($FMT$

/**************************************************************
 * 1. 原始格点 → tmp_raw
 *************************************************************/
CREATE TEMP TABLE tmp_raw ON COMMIT DROP AS
SELECT DISTINCT ON (r.cell_id)
       r.cell_id,
       r.%4$s                AS cell_value,
       %1$s(%2$s)            AS type,
       g.geom
FROM   %5$s r
JOIN   weather_grid g USING (cell_id)
WHERE  r.%4$s IS NOT NULL
  AND  %15$s
ORDER  BY r.cell_id, r.%4$s DESC, r.pre_time DESC;

/**************************************************************
 * 1.5 DBSCAN 聚类
 *************************************************************/
CREATE TEMP TABLE tmp_cluster ON COMMIT DROP AS
SELECT cell_id,
       type,
       ST_ClusterDBSCAN(geom, eps:=0.0001, minpoints:=1)
           OVER (PARTITION BY type ORDER BY cell_id) AS cid
FROM tmp_raw;

/**************************************************************
 * 2. 删除旧 Polygon 并生成新 Polygon
 *************************************************************/
DELETE FROM %3$s WHERE pre_time = $1 %13$s;

CREATE TEMP TABLE tmp_poly_map ON COMMIT DROP AS
WITH polys AS (
    SELECT  c.type, c.cid,
            MIN(r.cell_value) AS value_min,
            MAX(r.cell_value) AS value_max,
            ST_UnaryUnion(ST_Collect(r.geom)) AS poly
    FROM   tmp_cluster c
    JOIN   tmp_raw r USING(cell_id)
    GROUP  BY c.type, c.cid
),
ins AS (
    INSERT INTO %3$s
           (value_min, value_max, type, cid, pre_time,
            center, area, geometry%7$s)
    SELECT value_min, value_max, type, cid, $1,
           ST_PointOnSurface(poly),
           ST_Area(poly::geography),
           poly%8$s
    FROM polys
    RETURNING id, type, cid
)
SELECT type, cid, id FROM ins;

/**************************************************************
 * 3. 路段 Relation（range 并集 → 子串，**不合并**）
 *************************************************************/
WITH seg_raw AS (        /* 3.0 雨区内最小片段                       */
    SELECT  c.type, c.cid,
            l.route_id,
            l.route_code    AS road_num,
            l.route_name    AS road_name,
            l.pk_from,
            l.pk_to,
            l.management_office,
            l.maintenance_section,
            l.owner_type,
            l.management_office_id,
            l.maintenance_section_id,
            l.region_code,
            l.region_name
    FROM   tmp_cluster c
    JOIN   weather_cell_route_lut l USING (cell_id)
),
interval_src AS (        /* 3.1 把 pk 段转成 numeric range            */
    SELECT  type, cid,
            road_num, road_name,
            management_office, maintenance_section,
            owner_type,
            management_office_id, maintenance_section_id,
            region_code, region_name,
            numrange(pk_from, pk_to, '[]') AS rng
    FROM seg_raw
),
rng_merge AS (           /* 3.2 range_agg 并集 (PG16)                 */
    SELECT  type, cid,
            road_num, road_name,
            management_office, maintenance_section,
            owner_type,
            management_office_id, maintenance_section_id,
            region_code, region_name,
            range_agg(rng) AS mr
    FROM interval_src
    GROUP BY type, cid,
             road_num, road_name,
             management_office, maintenance_section,
             owner_type,
             management_office_id, maintenance_section_id,
             region_code, region_name
),
rng_exp AS (             /* 3.3 展平 multirange                       */
    SELECT  type, cid,
            road_num, road_name,
            management_office, maintenance_section,
            owner_type,
            management_office_id, maintenance_section_id,
            region_code, region_name,
            lower(r) AS pile_start,
            upper(r) AS pile_end
    FROM   rng_merge, unnest(mr) AS r
),
/*------------------------------------------------------------
 * 3.4 与 weather_routes 做子串裁剪（兼容 dir_sign）
 *-----------------------------------------------------------*/
clip_seg AS (
    /*------------ 外层：真正要输出的列 ----------------*/
    SELECT  x.type, x.cid,
            x.route_id                      AS road_id,
            x.road_num, x.road_name,

            x.pk_s          AS pile_start,
            x.pk_e          AS pile_end,
            x.management_office,
            x.maintenance_section,
            x.owner_type,
            x.management_office_id,
            x.maintenance_section_id,
            x.region_code,
            x.region_name,

            /* 方向兼容后的子串 */
            ST_LineSubstring(
                x.geometry,
                LEAST(frac.t0 , frac.t1),
                GREATEST(frac.t0 , frac.t1)
            ) AS shape
    FROM (
        /*------------ 内层 x：求交集桩号 + 基础字段 -------*/
        SELECT  e.type, e.cid,
                r.route_id,
                e.road_num, e.road_name,
                /* 交集桩号 */
                GREATEST(e.pile_start, r.pile_start) AS pk_s,
                LEAST   (e.pile_end  , r.pile_end )  AS pk_e,

                e.management_office,
                e.maintenance_section,
                e.owner_type,
                e.management_office_id,
                e.maintenance_section_id,
                e.region_code,
                e.region_name,

                r.dir_sign,
                r.geometry,
                r.pile_start AS r_pk_s,
                r.pile_end   AS r_pk_e,
                ABS(r.pile_end - r.pile_start) AS span_pk
        FROM   rng_exp e
        JOIN   %16$s  r
          ON   r.route_code             = e.road_num
         AND   r.management_office_id   = e.management_office_id
         AND   r.maintenance_section_id = e.maintenance_section_id
         AND   r.pile_end               >= e.pile_start
         AND   r.pile_start             <= e.pile_end
    ) AS x
    /*------------ 按方向把 pk → 0-1 比例 ----------------*/
    CROSS JOIN LATERAL (
        SELECT
            CASE
                WHEN x.span_pk = 0 THEN 0::numeric
                WHEN x.dir_sign >= 0          -- 正向
                     THEN (x.pk_s - x.r_pk_s) / x.span_pk
                ELSE                          -- 逆向
                     (x.r_pk_s - x.pk_s) / x.span_pk
            END AS t0,
            CASE
                WHEN x.span_pk = 0 THEN 1::numeric
                WHEN x.dir_sign >= 0
                     THEN (x.pk_e - x.r_pk_s) / x.span_pk
                ELSE
                     (x.r_pk_s - x.pk_e) / x.span_pk
            END AS t1
    ) AS frac
    /*------------ 过滤掉 0 长度片段 ----------------------*/
    WHERE x.pk_e - x.pk_s > 0
)
/*——— 3.5 clip_seg 就是最终结果，直接写入 relation 缓冲表 ——*/
INSERT INTO tmp_relation_buffer ( %12$s )
SELECT
    cs.road_id,
    cs.road_num,
    cs.road_name,
    cs.pile_start,
    cs.pile_end,
    cs.pile_end - cs.pile_start AS section_length,
    cs.management_office,
    cs.maintenance_section,
    cs.owner_type,
    cs.management_office_id,
    cs.maintenance_section_id,
    cs.region_code,
    cs.region_name,
    ST_LineInterpolatePoint(cs.shape, 0.5) AS center,
    cs.shape,
    mp.id,
    $1%9$s
FROM   clip_seg     cs
JOIN   tmp_poly_map mp
  ON   mp.type = cs.type
 AND   mp.cid  = cs.cid;

/*================== 动态 SQL 结束 ==================*/
$FMT$,
        q_calc_func,        -- %1$s  计算函数
        field_list,         -- %2$s  参数字段
        q_target_polygon,   -- %3$s
        q_value_field,      -- %4$s
        q_source_table,     -- %5$s
        q_target_relation,  -- %6$s (占位，当前未用)
        CASE WHEN need_poly_dur THEN ', forecast_duration' ELSE '' END,                               -- %7$s
        CASE WHEN need_poly_dur THEN
             ', CASE WHEN $2 IS NULL THEN NULL ELSE FLOOR(EXTRACT(EPOCH FROM ($2-$1))/3600 + 1)::int END'
             ELSE '' END,                                                                             -- %8$s
        CASE WHEN need_rel_dur THEN
             ', CASE WHEN $2 IS NULL THEN NULL ELSE FLOOR(EXTRACT(EPOCH FROM ($2-$1))/3600 + 1)::int END'
             ELSE '' END,                                                                             -- %9$s
        rel_columns,        -- %10$s
        rel_columns,        -- %11$s
        rel_columns,        -- %12$s
        del_poly_cond,      -- %13$s
        del_rel_cond,       -- %14$s
        raw_time_cond,      -- %15$s
        q_route_table       -- %16$s
    );

/*------------------------------------------------------------
 * 十、relation 缓冲临时表
 *-----------------------------------------------------------*/
    EXECUTE format(
        'CREATE TEMP TABLE IF NOT EXISTS tmp_relation_buffer
           (LIKE %s INCLUDING ALL) ON COMMIT DROP',
        q_target_relation);
    EXECUTE 'TRUNCATE tmp_relation_buffer';

/*------------------------------------------------------------
 * 十一、执行主体 SQL
 *-----------------------------------------------------------*/
    EXECUTE sql USING p_time, p_time_e;

/*------------------------------------------------------------
 * 十二、写回正式表
 *-----------------------------------------------------------*/
    EXECUTE format(
        'DELETE FROM %s WHERE pre_time = $1 %s',
        q_target_relation, del_rel_cond)
    USING p_time, p_time_e;

    EXECUTE format(
        'INSERT INTO %s ( %s ) SELECT %s FROM tmp_relation_buffer',
        q_target_relation, rel_columns, rel_columns);
END;
$BODY$;
    """
    return procedure_sql






def create_sp_precip_line_procedure():
    """
    创建功能完整且可配置的 sp_precip_line 存储过程
    """
    procedure_sql = """
/*=================================================================
 *  路段降水线生成过程  ──  “区间并集 + 母线子串（无合并）”  PG-16
 *  新增：
 *    ① 在目标表写入 region_code、region_name 两列
 *================================================================*/

DROP PROCEDURE IF EXISTS sp_precip_line (
       timestamp, timestamp,
       text, text[], text,
       text, text );

CREATE OR REPLACE PROCEDURE sp_precip_line (
    IN  p_time         timestamp,
    IN  p_time_e       timestamp DEFAULT NULL,
    IN  p_calc_func    text      DEFAULT 'get_rainfall_type',
    IN  p_arg_fields   text[]    DEFAULT ARRAY['rainfall','phase'],
    IN  p_source_table text      DEFAULT 'weather_cell_6m',
    IN  p_value_field  text      DEFAULT 'rainfall',
    IN  p_target_line  text      DEFAULT 'forecast_precipitation_6min_line'
)
LANGUAGE plpgsql
AS $BODY$
DECLARE
/*------------------------------------------------------------
 * 1. quote_ident / schema 名
 *-----------------------------------------------------------*/
    q_calc_func   text;
    q_source_tbl  text;
    q_target_tbl  text;
    q_value_fld   text;
    q_route_tbl   text := 'public.weather_routes';

    trg_schema    text;
    trg_table     text;

/*------------------------------------------------------------
 * 2. 动态字符串相关
 *-----------------------------------------------------------*/
    field_list        text;
    need_line_dur     boolean;
    dur_insert_expr   text;
    del_line_cond     text;
    raw_time_cond     text;
    ins_cols          text;
    sql               text;
BEGIN
/*------------------------------------------------------------
 * A. 会话级优化
 *-----------------------------------------------------------*/
    PERFORM set_config('work_mem','512MB',true);
    PERFORM set_config('synchronous_commit','off',true);
    SET CONSTRAINTS ALL DEFERRED;

/*------------------------------------------------------------
 * B. quote_ident & schema 解析
 *-----------------------------------------------------------*/
    q_calc_func :=
        CASE WHEN strpos(p_calc_func,'.')>0
             THEN quote_ident(split_part(p_calc_func,'.',1))||'.'||quote_ident(split_part(p_calc_func,'.',2))
             ELSE quote_ident(p_calc_func) END;

    q_source_tbl :=
        CASE WHEN strpos(p_source_table,'.')>0
             THEN quote_ident(split_part(p_source_table,'.',1))||'.'||quote_ident(split_part(p_source_table,'.',2))
             ELSE quote_ident(p_source_table) END;

    IF strpos(p_target_line,'.')>0 THEN
        trg_schema  := split_part(p_target_line,'.',1);
        trg_table   := split_part(p_target_line,'.',2);
        q_target_tbl:= quote_ident(trg_schema)||'.'||quote_ident(trg_table);
    ELSE
        trg_schema  := current_schema();
        trg_table   := p_target_line;
        q_target_tbl:= quote_ident(trg_table);
    END IF;

    q_value_fld := quote_ident(p_value_field);

/*------------------------------------------------------------
 * C. 动态字段清单
 *-----------------------------------------------------------*/
    SELECT string_agg('r.'||quote_ident(f),', ')
      INTO field_list
    FROM unnest(p_arg_fields) AS f;

/*------------------------------------------------------------
 * D. forecast_duration 字段
 *-----------------------------------------------------------*/
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
         WHERE table_schema = trg_schema
           AND table_name   = trg_table
           AND column_name  = 'forecast_duration'
    ) INTO need_line_dur;

    IF need_line_dur THEN
        IF p_time_e IS NULL THEN
            dur_insert_expr := ', NULL';
            del_line_cond   := 'AND forecast_duration IS NULL';
        ELSE
            dur_insert_expr := ', FLOOR(EXTRACT(EPOCH FROM ($2-$1))/3600 + 1)::int';
            del_line_cond   := 'AND forecast_duration = FLOOR(EXTRACT(EPOCH FROM ($2-$1))/3600 + 1)::int';
        END IF;
    ELSE
        dur_insert_expr := '';
        del_line_cond   := '';
    END IF;

/*------------------------------------------------------------
 * E. pre_time 过滤
 *-----------------------------------------------------------*/
    IF p_time_e IS NULL THEN
        raw_time_cond := 'r.pre_time = $1';
    ELSE
        raw_time_cond := 'r.pre_time >= $1 AND r.pre_time < $2';
    END IF;

/*------------------------------------------------------------
 * F. INSERT 列清单（新增 region_code / region_name）
 *-----------------------------------------------------------*/
    ins_cols := 'type, road_id, road_num, road_name,'||
                'pile_start, pile_end, section_length,'||
                'management_office, maintenance_section, owner_type,'||
                'region_code, region_name,'||
                'pre_time, management_office_id, maintenance_section_id,'||
                'center, shape, value_min, value_max'||
                CASE WHEN need_line_dur THEN ', forecast_duration' ELSE '' END;

/*------------------------------------------------------------
 * G. 主体动态 SQL
 *-----------------------------------------------------------*/
    sql := format($FMT$
/**************************************************************
 * 1. 最新格点记录
 *************************************************************/
CREATE TEMP TABLE tmp_raw_cell ON COMMIT DROP AS
SELECT DISTINCT ON (r.cell_id)
       r.cell_id,
       %1$s(%2$s) AS type,
       r.%4$s     AS value
FROM   %5$s r
WHERE  r.%4$s IS NOT NULL
  AND  %10$s
ORDER  BY r.cell_id, r.%4$s DESC, r.pre_time DESC;

/**************************************************************
 * 2. cell min / max
 *************************************************************/
CREATE TEMP TABLE tmp_cell_rain ON COMMIT DROP AS
SELECT cell_id,
       type,
       MIN(value) AS value_min,
       MAX(value) AS value_max
FROM   tmp_raw_cell
GROUP  BY cell_id,type;

/**************************************************************
 * 3. LUT 片段
 *    （新增 region_code / region_name）
 *************************************************************/
CREATE TEMP TABLE tmp_seg_raw ON COMMIT DROP AS
SELECT
    c.type,
    l.route_id,
    l.route_code,
    l.route_name,
    l.pk_from,
    l.pk_to,
    l.management_office,
    l.maintenance_section,
    l.owner_type,
    l.management_office_id,
    l.maintenance_section_id,
    l.region_code,
    l.region_name,
    c.value_min,
    c.value_max
FROM   tmp_cell_rain              c
JOIN   weather_cell_route_lut_line l USING(cell_id);

/**************************************************************
 * 4. range_agg 并集 → 展平
 *************************************************************/
CREATE TEMP TABLE tmp_rng_exp ON COMMIT DROP AS
WITH rng AS (
    SELECT  type,
            route_code,
            route_name,
            management_office,
            maintenance_section,
            owner_type,
            management_office_id,
            maintenance_section_id,
            region_code,
            region_name,
            range_agg( numrange(pk_from, pk_to,'[]') ) AS mr,
            MIN(value_min) AS vmin,
            MAX(value_max) AS vmax
    FROM   tmp_seg_raw
    GROUP  BY type, route_code, route_name,
             management_office, maintenance_section,
             owner_type, management_office_id, maintenance_section_id,
             region_code, region_name
)
SELECT  type,
        route_code,
        route_name,
        management_office,
        maintenance_section,
        owner_type,
        management_office_id,
        maintenance_section_id,
        region_code,
        region_name,
        vmin AS value_min,
        vmax AS value_max,
        lower(r) AS pile_start,
        upper(r) AS pile_end
FROM   rng , unnest(mr) AS r;

/**************************************************************
 * 5. 母线裁剪（不合并，每条母线独立截取）
 *************************************************************/
CREATE TEMP TABLE tmp_clip ON COMMIT DROP AS
SELECT
    e.type,
    r.route_id                     AS road_id,
    e.route_code                   AS road_num,
    e.route_name,
    GREATEST(e.pile_start, r.pile_start) AS pile_start,
    LEAST   (e.pile_end  , r.pile_end )  AS pile_end,
    e.management_office,
    e.maintenance_section,
    e.owner_type,
    e.management_office_id,
    e.maintenance_section_id,
    e.region_code,
    e.region_name,
    e.value_min,
    e.value_max,
    ST_LineSubstring(
        r.geometry,
        (GREATEST(e.pile_start, r.pile_start) - r.pile_start)
            / NULLIF(r.pile_end - r.pile_start,0),
        (LEAST   (e.pile_end  , r.pile_end  ) - r.pile_start)
            / NULLIF(r.pile_end - r.pile_start,0)
    ) AS shape
FROM   tmp_rng_exp  e
JOIN   %3$s        r
  ON   r.route_code             = e.route_code
 AND   r.management_office_id   = e.management_office_id
 AND   r.maintenance_section_id = e.maintenance_section_id
 AND   r.pile_end               >= e.pile_start
 AND   r.pile_start             <= e.pile_end
WHERE  LEAST(e.pile_end , r.pile_end )
     - GREATEST(e.pile_start, r.pile_start)  > 0;

/**************************************************************
 * 6. 删除旧数据 & 写入
 *************************************************************/
DELETE FROM %6$s
 WHERE pre_time = $1 %7$s;

INSERT INTO %6$s ( %8$s )
SELECT
    type,
    road_id,
    road_num,
    route_name,
    pile_start,
    pile_end,
    pile_end - pile_start               AS section_length,
    management_office,
    maintenance_section,
    owner_type,
    region_code,
    region_name,
    $1                                  AS pre_time,
    management_office_id,
    maintenance_section_id,
    CASE
        WHEN GeometryType(shape) LIKE 'LINE%%' AND NOT ST_IsEmpty(shape)
             THEN ST_LineInterpolatePoint(shape,0.5)
        ELSE ST_PointOnSurface(shape)
    END                                  AS center,
    shape,
    value_min,
    value_max
    %9$s
FROM tmp_clip;
$FMT$,
        q_calc_func,        -- %1$s  计算函数
        field_list,         -- %2$s  分级参数字段
        q_route_tbl,        -- %3$s  weather_routes
        q_value_fld,        -- %4$s  值字段
        q_source_tbl,       -- %5$s  cell 源表
        q_target_tbl,       -- %6$s  目标表
        del_line_cond,      -- %7$s  delete 附加条件
        ins_cols,           -- %8$s  INSERT 列清单
        dur_insert_expr,    -- %9$s  duration
        raw_time_cond       -- %10$s 时间过滤
    );

/*------------------------------------------------------------
 * H. 执行
 *-----------------------------------------------------------*/
    EXECUTE sql USING p_time, p_time_e;
END;
$BODY$;
    """
    return procedure_sql







def create_forecast_precipitation_6min_line_table():
    """
    创建 forecast_precipitation_6min_line 表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."forecast_precipitation_6min_line" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "type" int4,
      "value_min" numeric(8,3),
      "value_max" numeric(8,3),
      "road_id" int4,
      "road_num" varchar COLLATE "pg_catalog"."default",
      "road_name" varchar COLLATE "pg_catalog"."default",
      "region_code" varchar COLLATE "pg_catalog"."default",
      "region_name" varchar COLLATE "pg_catalog"."default",
      "pile_start" numeric(10,2),
      "pile_end" numeric(10,2),
      "section_length" numeric(10,2),
      "management_office" varchar COLLATE "pg_catalog"."default",
      "maintenance_section" varchar COLLATE "pg_catalog"."default",
      "owner_type" varchar COLLATE "pg_catalog"."default",
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      "management_office_id" varchar COLLATE "pg_catalog"."default",
      "maintenance_section_id" varchar COLLATE "pg_catalog"."default",
      "center" geometry(POINT, 4326),
      "shape" geometry(LINESTRING, 4326),
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "forecast_precipitation_6min_line_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."forecast_precipitation_6min_line" 
      OWNER TO "root";

    -- 注释掉空间索引：对插入敏感，经常更新的前端展示表不需要空间索引
    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_6min_line_center" ON "public"."forecast_precipitation_6min_line" USING gist (
    --   "center" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_6min_line_shape" ON "public"."forecast_precipitation_6min_line" USING gist (
    --   "shape" "public"."gist_geometry_ops_2d"
    -- );

    -- 添加删除操作优化索引：6min表根据pre_time删除
    CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_6min_line_pre_time" ON "public"."forecast_precipitation_6min_line" USING btree (
      "pre_time"
    );

    -- 创建region_code的text_pattern_ops索引，优化LIKE查询
    CREATE INDEX IF NOT EXISTS idx_fp6l_region_code_pattern
    ON public.forecast_precipitation_6min_line (region_code text_pattern_ops);

    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."value_min" IS '最小降雨/降雪量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."value_max" IS '最大降雨/降雪量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."type" IS '降雨类型 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨 11: 小雪 12: 中雪 13: 大雪 14: 暴雪';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."road_id" IS '路线ID - 关联base_road表';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."road_num" IS '路线编号';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."road_name" IS '路线名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."pile_start" IS '起始桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."pile_end" IS '结束桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."section_length" IS '路段长度(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."management_office" IS '管理单位名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."maintenance_section" IS '养护路段名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."owner_type" IS '产权类型';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."pre_time" IS '预报时间 - 对应天气数据的时间步';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."management_office_id" IS '管理单位ID - 关联sys_dept表';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."maintenance_section_id" IS '养护路段ID - 关联base_maintenance_section表';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."center" IS '路段中心点坐标 - WGS84坐标系';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_line"."shape" IS '路段几何形状 - WGS84坐标系的LineString';
    COMMENT ON TABLE "public"."forecast_precipitation_6min_line" IS '6分钟降水/降雪预报 - 存储所有时间步的路线降雨分析结果';
    """
    return create_table_sql

def create_forecast_precipitation_6min_polygon_table():
    """
    创建 forecast_precipitation_6min_polygon 表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."forecast_precipitation_6min_polygon" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "type" int4,
      "cid" int4,
       "value_min" numeric(8,3),
      "value_max" numeric(8,3),
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      "center" geometry(POINT, 4326),
      "area" numeric(15,2),
      "geometry" geometry(GEOMETRY, 4326),
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "forecast_precipitation_6min_polygon_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."forecast_precipitation_6min_polygon"
      OWNER TO "root";

    -- 注释掉空间索引：对插入敏感，经常更新的前端展示表不需要空间索引
    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_6min_polygon_center" ON "public"."forecast_precipitation_6min_polygon" USING gist (
    --   "center" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_6min_polygon_geometry" ON "public"."forecast_precipitation_6min_polygon" USING gist (
    --   "geometry" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_6min_polygon_type_cid" ON "public"."forecast_precipitation_6min_polygon" USING btree (
    --   "type", "cid"
    -- );

    -- 添加删除操作优化索引：6min表根据pre_time删除
    CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_6min_polygon_pre_time" ON "public"."forecast_precipitation_6min_polygon" USING btree (
      "pre_time"
    );

    COMMENT ON COLUMN "public"."forecast_precipitation_6min_polygon"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_polygon"."value_min" IS '最小降雨/降雪量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_polygon"."value_max" IS '最大降雨/降雪量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_polygon"."type" IS '降雨类型编码 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨 11:小雪 12:中雪 13:大雪 14:暴雪 15:大暴雪';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_polygon"."cid" IS '聚类ID - 用于标识同类型的相邻网格聚类';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_polygon"."pre_time" IS '预报时间 - 对应天气数据的时间步';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_polygon"."center" IS '多边形几何中心点 - WGS84坐标系';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_polygon"."area" IS '多边形面积 - 单位平方米';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_polygon"."geometry" IS '网格多边形几何形状 - WGS84坐标系，支持Polygon和MultiPolygon';
    COMMENT ON TABLE "public"."forecast_precipitation_6min_polygon" IS '6分钟降水/降雪预报 - 存储合并后的降雨网格区域';
    """
    return create_table_sql

def create_forecast_precipitation_6min_relation_table():
    """
    创建 forecast_precipitation_6min_relation 表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."forecast_precipitation_6min_relation" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "road_id" int4,
      "road_num" varchar COLLATE "pg_catalog"."default",
      "road_name" varchar COLLATE "pg_catalog"."default",
      "region_code" varchar COLLATE "pg_catalog"."default",
      "region_name" varchar COLLATE "pg_catalog"."default",
      "pile_start" numeric(10,2),
      "pile_end" numeric(10,2),
      "section_length" numeric(10,2),
      "management_office" varchar COLLATE "pg_catalog"."default",
      "maintenance_section" varchar COLLATE "pg_catalog"."default",
      "owner_type" varchar COLLATE "pg_catalog"."default",
      "management_office_id" varchar COLLATE "pg_catalog"."default",
      "maintenance_section_id" varchar COLLATE "pg_catalog"."default",
      "center" geometry(POINT, 4326),
      "shape" geometry(LINESTRING, 4326),
      "polygon_id" int4,
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "forecast_precipitation_6min_relation_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."forecast_precipitation_6min_relation"
      OWNER TO "root";

    -- 注释掉空间索引：对插入敏感，经常更新的前端展示表不需要空间索引
    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_6min_relation_center" ON "public"."forecast_precipitation_6min_relation" USING gist (
    --   "center" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_6min_relation_shape" ON "public"."forecast_precipitation_6min_relation" USING gist (
    --   "shape" "public"."gist_geometry_ops_2d"
    -- );

    -- 添加删除操作优化索引：6min表根据pre_time删除
    CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_6min_relation_pre_time" ON "public"."forecast_precipitation_6min_relation" USING btree (
      "pre_time"
    );

    -- 创建region_code的text_pattern_ops索引，优化LIKE查询
    CREATE INDEX IF NOT EXISTS idx_fp6r_region_code_pattern
    ON public.forecast_precipitation_6min_relation (region_code text_pattern_ops);

    -- 创建management_office_id和maintenance_section_id的复合索引，优化按管理单位和养护路段查询
    CREATE INDEX IF NOT EXISTS "forecast_precipitation_6min_r_management_office_id_maintena_idx" ON "public"."forecast_precipitation_6min_relation" USING btree (
      "management_office_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
      "maintenance_section_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."road_id" IS '路线ID - 关联base_road表';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."road_num" IS '路线编号';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."road_name" IS '路线名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."pile_start" IS '起始桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."pile_end" IS '结束桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."section_length" IS '路段长度(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."management_office" IS '管理单位名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."maintenance_section" IS '养护路段名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."owner_type" IS '产权类型';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."management_office_id" IS '管理单位ID - 关联sys_dept表';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."maintenance_section_id" IS '养护路段ID - 关联base_maintenance_section表';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."center" IS '路段中心点坐标 - WGS84坐标系';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."shape" IS '路段几何形状 - WGS84坐标系的LineString';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."polygon_id" IS 'forecast_precipitation_6min_polygon表的id';
    COMMENT ON COLUMN "public"."forecast_precipitation_6min_relation"."pre_time" IS '预报时间 - 对应天气数据的时间步';
    COMMENT ON TABLE "public"."forecast_precipitation_6min_relation" IS '6分钟降水/降雪预报 - 存储合并后的降雨网格区域影响路线关系';
    """
    return create_table_sql

def create_forecast_precipitation_hourly_line_table():
    """
    创建 forecast_precipitation_hourly_line 表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."forecast_precipitation_hourly_line" (
    id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "value_min" numeric(8,3),
    "value_max" numeric(8,3),
    "type" int4,
    "road_id" int4,
    "road_num" varchar COLLATE "pg_catalog"."default",
    "road_name" varchar COLLATE "pg_catalog"."default",
    "region_code" varchar COLLATE "pg_catalog"."default",
    "region_name" varchar COLLATE "pg_catalog"."default",
    "pile_start" numeric(10,2),
    "pile_end" numeric(10,2),
    "section_length" numeric(10,2),
    "management_office" varchar COLLATE "pg_catalog"."default",
    "maintenance_section" varchar COLLATE "pg_catalog"."default",
    "owner_type" varchar COLLATE "pg_catalog"."default",
    "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
    "management_office_id" varchar COLLATE "pg_catalog"."default",
    "maintenance_section_id" varchar COLLATE "pg_catalog"."default",
    "center" geometry(POINT, 4326),
    "shape" geometry(LINESTRING, 4326),
    -- 新的复合主键：分区键 + 原始ID
    CONSTRAINT "forecast_precipitation_hourly_line_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."forecast_precipitation_hourly_line" 
    OWNER TO "root";

    -- 注释掉空间索引：对插入敏感，经常更新的前端展示表不需要空间索引
    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_hourly_line_center" ON "public"."forecast_precipitation_hourly_line" USING gist (
    -- "center" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_hourly_line_shape" ON "public"."forecast_precipitation_hourly_line" USING gist (
    -- "shape" "public"."gist_geometry_ops_2d"
    -- );

    -- 添加删除操作优化索引：1h表根据pre_time删除
    CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_hourly_line_pre_time" ON "public"."forecast_precipitation_hourly_line" USING btree (
    "pre_time"
    );

    -- 创建region_code的text_pattern_ops索引，优化LIKE查询
    CREATE INDEX IF NOT EXISTS idx_fphl_region_code_pattern
    ON public.forecast_precipitation_hourly_line (region_code text_pattern_ops);

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."id" IS '主键ID - 自增长';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."value_min" IS '最小降雨/降雪量(mm) - 保留3位小数';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."value_max" IS '最大降雨/降雪量(mm) - 保留3位小数';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."type" IS '降雨类型 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨 11: 小雪 12: 中雪 13: 大雪 14: 暴雪';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."road_id" IS '路线ID - 关联base_road表';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."road_num" IS '路线编号';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."road_name" IS '路线名称';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."pile_start" IS '起始桩号(m) - 保留2位小数';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."pile_end" IS '结束桩号(m) - 保留2位小数';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."section_length" IS '路段长度(m) - 保留2位小数';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."management_office" IS '管理单位名称';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."maintenance_section" IS '养护路段名称';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."owner_type" IS '产权类型';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."pre_time" IS '预报时间 - 对应天气数据的时间步';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."management_office_id" IS '管理单位ID - 关联sys_dept表';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."maintenance_section_id" IS '养护路段ID - 关联base_maintenance_section表';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."center" IS '路段中心点坐标 - WGS84坐标系';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_line"."shape" IS '路段几何形状 - WGS84坐标系的LineString';

    COMMENT ON TABLE "public"."forecast_precipitation_hourly_line" IS '1小时的降水/降雪预报 - 存储所有时间步的路线降雨分析结果';
    """
    return create_table_sql

def create_forecast_precipitation_hourly_polygon_table():
    """
    创建 forecast_precipitation_hourly_polygon 表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."forecast_precipitation_hourly_polygon" (
    id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,

    "value_min" numeric(8,3),
    "value_max" numeric(8,3),
    "type" int4,
    "cid" int4,
    "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
    "center" geometry(POINT, 4326),
    "area" numeric(15,2),
    "geometry" geometry(GEOMETRY, 4326),
    -- 新的复合主键：分区键 + 原始ID
    CONSTRAINT "forecast_precipitation_hourly_polygon_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."forecast_precipitation_hourly_polygon"
    OWNER TO "root";

    -- 注释掉空间索引：对插入敏感，经常更新的前端展示表不需要空间索引
    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_hourly_polygon_center" ON "public"."forecast_precipitation_hourly_polygon" USING gist (
    -- "center" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_hourly_polygon_geometry" ON "public"."forecast_precipitation_hourly_polygon" USING gist (
    -- "geometry" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_hourly_polygon_type_cid" ON "public"."forecast_precipitation_hourly_polygon" USING btree (
    -- "type", "cid"
    -- );

    -- 添加删除操作优化索引：1h表根据pre_time删除
    CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_hourly_polygon_pre_time" ON "public"."forecast_precipitation_hourly_polygon" USING btree (
    "pre_time"
    );

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_polygon"."id" IS '主键ID - 自增长';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_polygon"."type" IS '降雨类型编码 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨 11:小雪 12:中雪 13:大雪 14:暴雪 15:大暴雪';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_polygon"."cid" IS '聚类ID - 用于标识同类型的相邻网格聚类';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_polygon"."pre_time" IS '预报时间 - 对应天气数据的时间步';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_polygon"."center" IS '多边形几何中心点 - WGS84坐标系';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_polygon"."area" IS '多边形面积 - 单位平方米';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_polygon"."geometry" IS '网格多边形几何形状 - WGS84坐标系，支持Polygon和MultiPolygon';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_polygon"."value_min" IS '最小降雨/降雪量(mm) - 保留3位小数';

    COMMENT ON COLUMN "public"."forecast_precipitation_hourly_polygon"."value_max" IS '最大降雨/降雪量(mm) - 保留3位小数';

    COMMENT ON TABLE "public"."forecast_precipitation_hourly_polygon" IS '1小时的降水/降雪预报 - 存储合并后的降雨网格区域';
    """
    return create_table_sql

def create_forecast_precipitation_hourly_relation_table():
    """
    创建 forecast_precipitation_hourly_relation 表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
        CREATE TABLE IF NOT EXISTS "public"."forecast_precipitation_hourly_relation" (
        id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
        "road_id" int4,
        "road_num" varchar COLLATE "pg_catalog"."default",
        "road_name" varchar COLLATE "pg_catalog"."default",
        "region_code" varchar COLLATE "pg_catalog"."default",
        "region_name" varchar COLLATE "pg_catalog"."default",
        "pile_start" numeric(10,2),
        "pile_end" numeric(10,2),
        "section_length" numeric(10,2),
        "management_office" varchar COLLATE "pg_catalog"."default",
        "maintenance_section" varchar COLLATE "pg_catalog"."default",
        "owner_type" varchar COLLATE "pg_catalog"."default",
        "management_office_id" varchar COLLATE "pg_catalog"."default",
        "maintenance_section_id" varchar COLLATE "pg_catalog"."default",
        "center" geometry(POINT, 4326),
        "shape" geometry(LINESTRING, 4326),
        "polygon_id" int4,
        "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
        -- 新的复合主键：分区键 + 原始ID
        CONSTRAINT "forecast_precipitation_hourly_relation_pkey" PRIMARY KEY (pre_time, id)
        ) PARTITION BY RANGE (pre_time);

        ALTER TABLE "public"."forecast_precipitation_hourly_relation" 
        OWNER TO "root";

        -- 注释掉空间索引：对插入敏感，经常更新的前端展示表不需要空间索引
        -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_hourly_relation_center" ON "public"."forecast_precipitation_hourly_relation" USING gist (
        -- "center" "public"."gist_geometry_ops_2d"
        -- );

        -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_hourly_relation_shape" ON "public"."forecast_precipitation_hourly_relation" USING gist (
        -- "shape" "public"."gist_geometry_ops_2d"
        -- );

        -- 添加删除操作优化索引：1h表根据pre_time删除
        CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_hourly_relation_pre_time" ON "public"."forecast_precipitation_hourly_relation" USING btree (
        "pre_time"
        );

        -- 创建region_code的text_pattern_ops索引，优化LIKE查询
        CREATE INDEX IF NOT EXISTS idx_fphr_region_code_pattern
        ON public.forecast_precipitation_hourly_relation (region_code text_pattern_ops);

        -- 创建management_office_id和maintenance_section_id的复合索引，优化按管理单位和养护路段查询
        CREATE INDEX IF NOT EXISTS "forecast_precipitation_hourly_r_management_office_id_maintena_idx" ON "public"."forecast_precipitation_hourly_relation" USING btree (
          "management_office_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
          "maintenance_section_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
        );

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."id" IS '主键ID - 自增长';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."road_id" IS '路线ID - 关联base_road表';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."road_num" IS '路线编号';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."road_name" IS '路线名称';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."pile_start" IS '起始桩号(m) - 保留2位小数';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."pile_end" IS '结束桩号(m) - 保留2位小数';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."section_length" IS '路段长度(m) - 保留2位小数';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."management_office" IS '管理单位名称';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."maintenance_section" IS '养护路段名称';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."owner_type" IS '产权类型';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."management_office_id" IS '管理单位ID - 关联sys_dept表';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."maintenance_section_id" IS '养护路段ID - 关联base_maintenance_section表';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."center" IS '路段中心点坐标 - WGS84坐标系';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."shape" IS '路段几何形状 - WGS84坐标系的LineString';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."polygon_id" IS 'forecast_precipitation_hourly_polygon表的id';

        COMMENT ON COLUMN "public"."forecast_precipitation_hourly_relation"."pre_time" IS '预报时间 - 对应天气数据的时间步';

        COMMENT ON TABLE "public"."forecast_precipitation_hourly_relation" IS '1小时的降水/降雪预报 - 存储合并后的降雨网格区域影响路线关系';
    """
    return create_table_sql

def create_forecast_precipitation_summary_line_table():
    """
    创建 forecast_precipitation_summary_line 表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."forecast_precipitation_summary_line" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "value_min" numeric(8,3),
      "value_max" numeric(8,3),
      "type" int4,
      "road_id" int4,
      "road_num" varchar COLLATE "pg_catalog"."default",
      "road_name" varchar COLLATE "pg_catalog"."default",
      "region_code" varchar COLLATE "pg_catalog"."default",
      "region_name" varchar COLLATE "pg_catalog"."default",
      "pile_start" numeric(10,2),
      "pile_end" numeric(10,2),
      "section_length" numeric(10,2),
      "management_office" varchar COLLATE "pg_catalog"."default",
      "maintenance_section" varchar COLLATE "pg_catalog"."default",
      "owner_type" varchar COLLATE "pg_catalog"."default",
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      "management_office_id" varchar COLLATE "pg_catalog"."default",
      "maintenance_section_id" varchar COLLATE "pg_catalog"."default",
      "forecast_duration" int4,
      "center" geometry(POINT, 4326),
      "shape" geometry(LINESTRING, 4326),
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "forecast_precipitation_summary_line_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."forecast_precipitation_summary_line"
      OWNER TO "root";

    -- 注释掉空间索引：对插入敏感，经常更新的前端展示表不需要空间索引
    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_summary_line_center" ON "public"."forecast_precipitation_summary_line" USING gist (
    --   "center" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_summary_line_shape" ON "public"."forecast_precipitation_summary_line" USING gist (
    --   "shape" "public"."gist_geometry_ops_2d"
    -- );

    -- 添加删除操作优化索引：summary表根据pre_time和forecast_duration删除
    CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_summary_line_pre_time_duration" ON "public"."forecast_precipitation_summary_line" USING btree (
      "pre_time", "forecast_duration"
    );

    -- 创建region_code的text_pattern_ops索引，优化LIKE查询
    CREATE INDEX IF NOT EXISTS idx_fpsl_region_code_pattern
    ON public.forecast_precipitation_summary_line (region_code text_pattern_ops);

    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."value_min" IS '最小降雨/降雪量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."value_max" IS '最大降雨/降雪量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."type" IS '降雨类型 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨 11: 小雪 12: 中雪 13: 大雪 14: 暴雪';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."road_id" IS '路线ID - 关联base_road表';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."road_num" IS '路线编号';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."road_name" IS '路线名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."pile_start" IS '起始桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."pile_end" IS '结束桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."section_length" IS '路段长度(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."management_office" IS '管理单位名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."maintenance_section" IS '养护路段名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."owner_type" IS '产权类型';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."pre_time" IS '预报时间 - 对应天气数据的时间步';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."management_office_id" IS '管理单位ID - 关联sys_dept表';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."maintenance_section_id" IS '养护路段ID - 关联base_maintenance_section表';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."forecast_duration" IS '预报时长（小时），例如：24、48、72';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."center" IS '路段中心点坐标 - WGS84坐标系';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_line"."shape" IS '路段几何形状 - WGS84坐标系的LineString';
    COMMENT ON TABLE "public"."forecast_precipitation_summary_line" IS '叠加的降水/降雪预报 - 存储所有时间步的路线降雨分析结果';
    """
    return create_table_sql

def create_forecast_precipitation_summary_polygon_table():
    """
    创建 forecast_precipitation_summary_polygon 表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."forecast_precipitation_summary_polygon" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "value_min" numeric(8,3),
      "value_max" numeric(8,3),
      "type" int4,
      "cid" int4,
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      "center" geometry(POINT, 4326),
      "area" numeric(15,2),
      "geometry" geometry(GEOMETRY, 4326),
      "forecast_duration" int4,
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "forecast_precipitation_summary_polygon_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."forecast_precipitation_summary_polygon"
      OWNER TO "root";

    -- 注释掉空间索引：对插入敏感，经常更新的前端展示表不需要空间索引
    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_summary_polygon_center" ON "public"."forecast_precipitation_summary_polygon" USING gist (
    --   "center" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_summary_polygon_geometry" ON "public"."forecast_precipitation_summary_polygon" USING gist (
    --   "geometry" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_summary_polygon_type_cid" ON "public"."forecast_precipitation_summary_polygon" USING btree (
    --   "type", "cid"
    -- );

    -- 添加删除操作优化索引：summary表根据pre_time和forecast_duration删除
    CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_summary_polygon_pre_time_duration" ON "public"."forecast_precipitation_summary_polygon" USING btree (
      "pre_time", "forecast_duration"
    );

    COMMENT ON COLUMN "public"."forecast_precipitation_summary_polygon"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_polygon"."value_min" IS '最小降雨/降雪量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_polygon"."value_max" IS '最大降雨/降雪量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_polygon"."type" IS '降雨类型 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨 11: 小雪 12: 中雪 13: 大雪 14: 暴雪';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_polygon"."cid" IS '聚类ID - 用于标识同类型的相邻网格聚类';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_polygon"."pre_time" IS '预报时间 - 对应天气数据的时间步';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_polygon"."center" IS '多边形几何中心点 - WGS84坐标系';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_polygon"."area" IS '多边形面积 - 单位平方米';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_polygon"."geometry" IS '网格多边形几何形状 - WGS84坐标系，支持Polygon和MultiPolygon';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_polygon"."forecast_duration" IS '预报时长（小时），例如：24、48、72';
    COMMENT ON TABLE "public"."forecast_precipitation_summary_polygon" IS '叠加的降水/降雪预报 - 存储合并后的降雨网格区域';
    """
    return create_table_sql

def create_forecast_precipitation_summary_relation_table():
    """
    创建 forecast_precipitation_summary_relation 表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."forecast_precipitation_summary_relation" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "road_id" int4,
      "road_num" varchar COLLATE "pg_catalog"."default",
      "road_name" varchar COLLATE "pg_catalog"."default",
      "region_code" varchar COLLATE "pg_catalog"."default",
      "region_name" varchar COLLATE "pg_catalog"."default",
      "pile_start" numeric(10,2),
      "pile_end" numeric(10,2),
      "section_length" numeric(10,2),
      "management_office" varchar COLLATE "pg_catalog"."default",
      "maintenance_section" varchar COLLATE "pg_catalog"."default",
      "owner_type" varchar COLLATE "pg_catalog"."default",
      "management_office_id" varchar COLLATE "pg_catalog"."default",
      "maintenance_section_id" varchar COLLATE "pg_catalog"."default",
      "center" geometry(POINT, 4326),
      "shape" geometry(LINESTRING, 4326),
      "polygon_id" int4,
      "value_min" numeric(8,3),
      "value_max" numeric(8,3),
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      "forecast_duration" int4,
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "forecast_precipitation_summary_relation_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."forecast_precipitation_summary_relation"
      OWNER TO "root";

    -- 注释掉空间索引：对插入敏感，经常更新的前端展示表不需要空间索引
    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_summary_relation_center" ON "public"."forecast_precipitation_summary_relation" USING gist (
    --   "center" "public"."gist_geometry_ops_2d"
    -- );

    -- CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_summary_relation_shape" ON "public"."forecast_precipitation_summary_relation" USING gist (
    --   "shape" "public"."gist_geometry_ops_2d"
    -- );

    -- 添加删除操作优化索引：summary表根据pre_time和forecast_duration删除
    CREATE INDEX IF NOT EXISTS "idx_forecast_precipitation_summary_relation_pre_time_duration" ON "public"."forecast_precipitation_summary_relation" USING btree (
      "pre_time", "forecast_duration"
    );

    -- 创建region_code的text_pattern_ops索引，优化LIKE查询
    CREATE INDEX IF NOT EXISTS idx_fpsr_region_code_pattern
    ON public.forecast_precipitation_summary_relation (region_code text_pattern_ops);

    -- 创建management_office_id和maintenance_section_id的复合索引，优化按管理单位和养护路段查询
    CREATE INDEX IF NOT EXISTS "forecast_precipitation_summary_r_management_office_id_maintena_idx" ON "public"."forecast_precipitation_summary_relation" USING btree (
      "management_office_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
      "maintenance_section_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."road_id" IS '路线ID - 关联base_road表';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."road_num" IS '路线编号';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."road_name" IS '路线名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."pile_start" IS '起始桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."pile_end" IS '结束桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."section_length" IS '路段长度(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."management_office" IS '管理单位名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."maintenance_section" IS '养护路段名称';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."owner_type" IS '产权类型';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."management_office_id" IS '管理单位ID - 关联sys_dept表';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."maintenance_section_id" IS '养护路段ID - 关联base_maintenance_section表';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."center" IS '路段中心点坐标 - WGS84坐标系';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."shape" IS '路段几何形状 - WGS84坐标系的LineString';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."polygon_id" IS 'forecast_precipitation_summary_polygon表的id';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."value_min" IS '最小降雨量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."value_max" IS '最大降雨量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."pre_time" IS '预报时间 - 对应天气数据的时间步';
    COMMENT ON COLUMN "public"."forecast_precipitation_summary_relation"."forecast_duration" IS '预报时长（小时），例如：24、48、72';
    COMMENT ON TABLE "public"."forecast_precipitation_summary_relation" IS '叠加的降水/降雪预报 - 存储合并后的降雨网格区域影响路线关系';
    """
    return create_table_sql


def create_obs_precipitation_10min_line_table():
    """
    创建实况10分钟降水线数据表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."obs_precipitation_10min_line" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "type" int4,
      "value_min" numeric(8,3),
      "value_max" numeric(8,3),
      "road_id" int4,
      "road_num" varchar COLLATE "pg_catalog"."default",
      "road_name" varchar COLLATE "pg_catalog"."default",
      "region_code" varchar COLLATE "pg_catalog"."default",
      "region_name" varchar COLLATE "pg_catalog"."default",
      "pile_start" numeric(10,2),
      "pile_end" numeric(10,2),
      "section_length" numeric(10,2),
      "management_office" varchar COLLATE "pg_catalog"."default",
      "maintenance_section" varchar COLLATE "pg_catalog"."default",
      "owner_type" varchar COLLATE "pg_catalog"."default",
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      "management_office_id" varchar COLLATE "pg_catalog"."default",
      "maintenance_section_id" varchar COLLATE "pg_catalog"."default",
      "center" geometry(POINT, 4326),
      "shape" geometry(LINESTRING, 4326),
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "obs_precipitation_10min_line_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."obs_precipitation_10min_line"
      OWNER TO "root";

    -- 添加删除操作优化索引
    CREATE INDEX IF NOT EXISTS "idx_obs_precipitation_10min_line_pre_time" ON "public"."obs_precipitation_10min_line" USING btree (
      "pre_time"
    );

    -- 创建region_code的text_pattern_ops索引，优化LIKE查询
    CREATE INDEX IF NOT EXISTS idx_op10l_region_code_pattern
    ON public.obs_precipitation_10min_line (region_code text_pattern_ops);

    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."value_min" IS '最小降雨量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."value_max" IS '最大降雨量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."type" IS '降雨类型 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."road_id" IS '路线ID - 关联base_road表';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."road_num" IS '路线编号';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."road_name" IS '路线名称';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."pile_start" IS '起始桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."pile_end" IS '结束桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."section_length" IS '路段长度(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."management_office" IS '管理单位名称';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."maintenance_section" IS '养护路段名称';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."owner_type" IS '产权类型';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."pre_time" IS '实况时间 - 对应实况数据的时间';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."management_office_id" IS '管理单位ID - 关联sys_dept表';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."maintenance_section_id" IS '养护路段ID - 关联base_maintenance_section表';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."center" IS '路段中心点坐标 - WGS84坐标系';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_line"."shape" IS '路段几何形状 - WGS84坐标系的LineString';
    COMMENT ON TABLE "public"."obs_precipitation_10min_line" IS '实况10分钟降水 - 存储路线降雨分析结果';
    """
    return create_table_sql


def create_obs_precipitation_10min_polygon_table():
    """
    创建实况10分钟降水面数据表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."obs_precipitation_10min_polygon" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "type" int4,
      "cid" int4,
      "value_min" numeric(8,3),
      "value_max" numeric(8,3),
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      "center" geometry(POINT, 4326),
      "area" numeric(15,2),
      "geometry" geometry(GEOMETRY, 4326),
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "obs_precipitation_10min_polygon_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."obs_precipitation_10min_polygon"
      OWNER TO "root";

    -- 添加删除操作优化索引
    CREATE INDEX IF NOT EXISTS "idx_obs_precipitation_10min_polygon_pre_time" ON "public"."obs_precipitation_10min_polygon" USING btree (
      "pre_time"
    );

    COMMENT ON COLUMN "public"."obs_precipitation_10min_polygon"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_polygon"."value_min" IS '最小降雨量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_polygon"."value_max" IS '最大降雨量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_polygon"."type" IS '降雨类型编码 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_polygon"."cid" IS '聚类ID - 用于标识同类型的相邻网格聚类';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_polygon"."pre_time" IS '实况时间 - 对应实况数据的时间';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_polygon"."center" IS '多边形几何中心点 - WGS84坐标系';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_polygon"."area" IS '多边形面积 - 单位平方米';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_polygon"."geometry" IS '网格多边形几何形状 - WGS84坐标系，支持Polygon和MultiPolygon';
    COMMENT ON TABLE "public"."obs_precipitation_10min_polygon" IS '实况10分钟降水 - 存储合并后的降雨网格区域';
    """
    return create_table_sql


def create_obs_precipitation_10min_relation_table():
    """
    创建实况10分钟降水关系表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."obs_precipitation_10min_relation" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "road_id" int4,
      "road_num" varchar COLLATE "pg_catalog"."default",
      "road_name" varchar COLLATE "pg_catalog"."default",
      "region_code" varchar COLLATE "pg_catalog"."default",
      "region_name" varchar COLLATE "pg_catalog"."default",
      "pile_start" numeric(10,2),
      "pile_end" numeric(10,2),
      "section_length" numeric(10,2),
      "management_office" varchar COLLATE "pg_catalog"."default",
      "maintenance_section" varchar COLLATE "pg_catalog"."default",
      "owner_type" varchar COLLATE "pg_catalog"."default",
      "management_office_id" varchar COLLATE "pg_catalog"."default",
      "maintenance_section_id" varchar COLLATE "pg_catalog"."default",
      "center" geometry(POINT, 4326),
      "shape" geometry(LINESTRING, 4326),
      "polygon_id" int4,
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "obs_precipitation_10min_relation_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."obs_precipitation_10min_relation"
      OWNER TO "root";

    -- 添加删除操作优化索引
    CREATE INDEX IF NOT EXISTS "idx_obs_precipitation_10min_relation_pre_time" ON "public"."obs_precipitation_10min_relation" USING btree (
      "pre_time"
    );

    -- 创建region_code的text_pattern_ops索引，优化LIKE查询
    CREATE INDEX IF NOT EXISTS idx_op10r_region_code_pattern
    ON public.obs_precipitation_10min_relation (region_code text_pattern_ops);

    -- 创建management_office_id和maintenance_section_id的复合索引
    CREATE INDEX IF NOT EXISTS "obs_precipitation_10min_r_management_office_id_maintena_idx" ON "public"."obs_precipitation_10min_relation" USING btree (
      "management_office_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
      "maintenance_section_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."road_id" IS '路线ID - 关联base_road表';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."road_num" IS '路线编号';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."road_name" IS '路线名称';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."pile_start" IS '起始桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."pile_end" IS '结束桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."section_length" IS '路段长度(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."management_office" IS '管理单位名称';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."maintenance_section" IS '养护路段名称';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."owner_type" IS '产权类型';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."management_office_id" IS '管理单位ID - 关联sys_dept表';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."maintenance_section_id" IS '养护路段ID - 关联base_maintenance_section表';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."center" IS '路段中心点坐标 - WGS84坐标系';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."shape" IS '路段几何形状 - WGS84坐标系的LineString';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."polygon_id" IS 'obs_precipitation_10min_polygon表的id';
    COMMENT ON COLUMN "public"."obs_precipitation_10min_relation"."pre_time" IS '实况时间 - 对应实况数据的时间';
    COMMENT ON TABLE "public"."obs_precipitation_10min_relation" IS '实况10分钟降水 - 存储合并后的降雨网格区域影响路线关系';
    """
    return create_table_sql


def create_obs_precipitation_1h_line_table():
    """
    创建实况1小时降水线数据表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."obs_precipitation_1h_line" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "type" int4,
      "value_min" numeric(8,3),
      "value_max" numeric(8,3),
      "road_id" int4,
      "road_num" varchar COLLATE "pg_catalog"."default",
      "road_name" varchar COLLATE "pg_catalog"."default",
      "region_code" varchar COLLATE "pg_catalog"."default",
      "region_name" varchar COLLATE "pg_catalog"."default",
      "pile_start" numeric(10,2),
      "pile_end" numeric(10,2),
      "section_length" numeric(10,2),
      "management_office" varchar COLLATE "pg_catalog"."default",
      "maintenance_section" varchar COLLATE "pg_catalog"."default",
      "owner_type" varchar COLLATE "pg_catalog"."default",
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      "management_office_id" varchar COLLATE "pg_catalog"."default",
      "maintenance_section_id" varchar COLLATE "pg_catalog"."default",
      "center" geometry(POINT, 4326),
      "shape" geometry(LINESTRING, 4326),
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "obs_precipitation_1h_line_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."obs_precipitation_1h_line"
      OWNER TO "root";

    -- 添加删除操作优化索引
    CREATE INDEX IF NOT EXISTS "idx_obs_precipitation_1h_line_pre_time" ON "public"."obs_precipitation_1h_line" USING btree (
      "pre_time"
    );

    -- 创建region_code的text_pattern_ops索引，优化LIKE查询
    CREATE INDEX IF NOT EXISTS idx_op1hl_region_code_pattern
    ON public.obs_precipitation_1h_line (region_code text_pattern_ops);

    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."value_min" IS '最小降雨量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."value_max" IS '最大降雨量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."type" IS '降雨类型 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."road_id" IS '路线ID - 关联base_road表';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."road_num" IS '路线编号';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."road_name" IS '路线名称';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."pile_start" IS '起始桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."pile_end" IS '结束桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."section_length" IS '路段长度(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."management_office" IS '管理单位名称';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."maintenance_section" IS '养护路段名称';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."owner_type" IS '产权类型';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."pre_time" IS '实况时间 - 对应实况数据的时间';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."management_office_id" IS '管理单位ID - 关联sys_dept表';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."maintenance_section_id" IS '养护路段ID - 关联base_maintenance_section表';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."center" IS '路段中心点坐标 - WGS84坐标系';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_line"."shape" IS '路段几何形状 - WGS84坐标系的LineString';
    COMMENT ON TABLE "public"."obs_precipitation_1h_line" IS '实况1小时降水 - 存储路线降雨分析结果';
    """
    return create_table_sql


def create_obs_precipitation_1h_polygon_table():
    """
    创建实况1小时降水面数据表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."obs_precipitation_1h_polygon" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "type" int4,
      "cid" int4,
      "value_min" numeric(8,3),
      "value_max" numeric(8,3),
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      "center" geometry(POINT, 4326),
      "area" numeric(15,2),
      "geometry" geometry(GEOMETRY, 4326),
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "obs_precipitation_1h_polygon_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."obs_precipitation_1h_polygon"
      OWNER TO "root";

    -- 添加删除操作优化索引
    CREATE INDEX IF NOT EXISTS "idx_obs_precipitation_1h_polygon_pre_time" ON "public"."obs_precipitation_1h_polygon" USING btree (
      "pre_time"
    );

    COMMENT ON COLUMN "public"."obs_precipitation_1h_polygon"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_polygon"."value_min" IS '最小降雨量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_polygon"."value_max" IS '最大降雨量(mm) - 保留3位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_polygon"."type" IS '降雨类型编码 - 0:无雨 1:小雨 2:中雨 3:大雨 4:暴雨 5:大暴雨 6:特大暴雨';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_polygon"."cid" IS '聚类ID - 用于标识同类型的相邻网格聚类';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_polygon"."pre_time" IS '实况时间 - 对应实况数据的时间';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_polygon"."center" IS '多边形几何中心点 - WGS84坐标系';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_polygon"."area" IS '多边形面积 - 单位平方米';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_polygon"."geometry" IS '网格多边形几何形状 - WGS84坐标系，支持Polygon和MultiPolygon';
    COMMENT ON TABLE "public"."obs_precipitation_1h_polygon" IS '实况1小时降水 - 存储合并后的降雨网格区域';
    """
    return create_table_sql


def create_obs_precipitation_1h_relation_table():
    """
    创建实况1小时降水关系表
    按照分区改造方案B：使用(pre_time, id)作为复合主键，按pre_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."obs_precipitation_1h_relation" (
      id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,
      "road_id" int4,
      "road_num" varchar COLLATE "pg_catalog"."default",
      "road_name" varchar COLLATE "pg_catalog"."default",
      "region_code" varchar COLLATE "pg_catalog"."default",
      "region_name" varchar COLLATE "pg_catalog"."default",
      "pile_start" numeric(10,2),
      "pile_end" numeric(10,2),
      "section_length" numeric(10,2),
      "management_office" varchar COLLATE "pg_catalog"."default",
      "maintenance_section" varchar COLLATE "pg_catalog"."default",
      "owner_type" varchar COLLATE "pg_catalog"."default",
      "management_office_id" varchar COLLATE "pg_catalog"."default",
      "maintenance_section_id" varchar COLLATE "pg_catalog"."default",
      "center" geometry(POINT, 4326),
      "shape" geometry(LINESTRING, 4326),
      "polygon_id" int4,
      "pre_time" timestamp(6) NOT NULL,                                    -- 分区键，必须非空
      -- 新的复合主键：分区键 + 原始ID
      CONSTRAINT "obs_precipitation_1h_relation_pkey" PRIMARY KEY (pre_time, id)
    ) PARTITION BY RANGE (pre_time);

    ALTER TABLE "public"."obs_precipitation_1h_relation"
      OWNER TO "root";

    -- 添加删除操作优化索引
    CREATE INDEX IF NOT EXISTS "idx_obs_precipitation_1h_relation_pre_time" ON "public"."obs_precipitation_1h_relation" USING btree (
      "pre_time"
    );

    -- 创建region_code的text_pattern_ops索引，优化LIKE查询
    CREATE INDEX IF NOT EXISTS idx_op1hr_region_code_pattern
    ON public.obs_precipitation_1h_relation (region_code text_pattern_ops);

    -- 创建management_office_id和maintenance_section_id的复合索引
    CREATE INDEX IF NOT EXISTS "obs_precipitation_1h_r_management_office_id_maintena_idx" ON "public"."obs_precipitation_1h_relation" USING btree (
      "management_office_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
      "maintenance_section_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."id" IS '主键ID - 自增长';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."road_id" IS '路线ID - 关联base_road表';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."road_num" IS '路线编号';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."road_name" IS '路线名称';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."pile_start" IS '起始桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."pile_end" IS '结束桩号(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."section_length" IS '路段长度(m) - 保留2位小数';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."management_office" IS '管理单位名称';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."maintenance_section" IS '养护路段名称';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."owner_type" IS '产权类型';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."management_office_id" IS '管理单位ID - 关联sys_dept表';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."maintenance_section_id" IS '养护路段ID - 关联base_maintenance_section表';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."center" IS '路段中心点坐标 - WGS84坐标系';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."shape" IS '路段几何形状 - WGS84坐标系的LineString';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."polygon_id" IS 'obs_precipitation_1h_polygon表的id';
    COMMENT ON COLUMN "public"."obs_precipitation_1h_relation"."pre_time" IS '实况时间 - 对应实况数据的时间';
    COMMENT ON TABLE "public"."obs_precipitation_1h_relation" IS '实况1小时降水 - 存储合并后的降雨网格区域影响路线关系';
    """
    return create_table_sql


def create_weather_alarm_table():
    """
    创建天气预警信息表
    按照分区改造方案C：使用(publish_time, id)作为复合主键，按publish_time分区
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS "public"."weather_alarm" (
    id BIGINT NOT NULL GENERATED BY DEFAULT AS IDENTITY,                -- 不再是主键，保留作为行标识符
    "weather_id" INTEGER NOT NULL,
    "region_code" VARCHAR(20) NOT NULL,
    "province_name" VARCHAR(100),
    "city_name" VARCHAR(100),
    "county_name" VARCHAR(100),
    "sub_code" VARCHAR(500),
    "sub_name" VARCHAR(500),
    "alarm_category_code" VARCHAR(10),
    "alarm_category_name" VARCHAR(50),
    "alarm_level_code" VARCHAR(10),
    "alarm_level_name" VARCHAR(50),
    "publish_time" TIMESTAMP NOT NULL,                                   -- 分区键，必须非空
    "alarm_content" TEXT,
    "alarm_info" VARCHAR(500),
    "jump_url" VARCHAR(200),
    "alarm_title" VARCHAR(200),
    "publish_status" VARCHAR(20),
    "cancel_time" TIMESTAMP,
    "sms_status" VARCHAR(1) NOT NULL DEFAULT '0',
    -- 新的复合主键：分区键 + 原始ID
    CONSTRAINT "weather_alarm_pkey" PRIMARY KEY (publish_time, id)
    ) PARTITION BY RANGE (publish_time);

    -- 创建索引
    CREATE INDEX IF NOT EXISTS "idx_weather_alarm_weather_id"
    ON "public"."weather_alarm" ("weather_id");

    CREATE INDEX IF NOT EXISTS "idx_weather_alarm_region_code"
    ON "public"."weather_alarm" ("region_code");

    CREATE INDEX IF NOT EXISTS "idx_weather_alarm_publish_time"
    ON "public"."weather_alarm" ("publish_time");

    CREATE INDEX IF NOT EXISTS "idx_weather_alarm_alarm_info"
    ON "public"."weather_alarm" ("alarm_info");

    CREATE INDEX IF NOT EXISTS "idx_weather_alarm_cancel_time"
    ON "public"."weather_alarm" ("cancel_time");

    -- 创建唯一索引，防止重复数据（必须包含分区键publish_time）
    CREATE UNIQUE INDEX IF NOT EXISTS "idx_weather_alarm_unique"
    ON "public"."weather_alarm" (publish_time, "weather_id", "alarm_info");

    ALTER TABLE "public"."weather_alarm" OWNER TO "root";

    -- 添加字段注释
    COMMENT ON COLUMN "public"."weather_alarm"."id" IS '主键ID';
    COMMENT ON COLUMN "public"."weather_alarm"."weather_id" IS '气象ID';
    COMMENT ON COLUMN "public"."weather_alarm"."region_code" IS '区划代码';
    COMMENT ON COLUMN "public"."weather_alarm"."province_name" IS '省级名称';
    COMMENT ON COLUMN "public"."weather_alarm"."city_name" IS '市级名称';
    COMMENT ON COLUMN "public"."weather_alarm"."county_name" IS '县级名称';
    COMMENT ON COLUMN "public"."weather_alarm"."sub_code" IS '子区域代码（逗号分隔）';
    COMMENT ON COLUMN "public"."weather_alarm"."sub_name" IS '子区域名称（逗号分隔）';
    COMMENT ON COLUMN "public"."weather_alarm"."alarm_category_code" IS '预警类别编号';
    COMMENT ON COLUMN "public"."weather_alarm"."alarm_category_name" IS '预警类别名称';
    COMMENT ON COLUMN "public"."weather_alarm"."alarm_level_code" IS '预警级别编号';
    COMMENT ON COLUMN "public"."weather_alarm"."alarm_level_name" IS '预警级别名称';
    COMMENT ON COLUMN "public"."weather_alarm"."publish_time" IS '预警发布时间';
    COMMENT ON COLUMN "public"."weather_alarm"."alarm_content" IS '预警发布内容';
    COMMENT ON COLUMN "public"."weather_alarm"."alarm_info" IS '预警信息';
    COMMENT ON COLUMN "public"."weather_alarm"."jump_url" IS '天气网跳转地址';
    COMMENT ON COLUMN "public"."weather_alarm"."alarm_title" IS '预警标题';
    COMMENT ON COLUMN "public"."weather_alarm"."publish_status" IS '发布状态(Alert/Update)';
    COMMENT ON COLUMN "public"."weather_alarm"."cancel_time" IS '预警解除时间';
    COMMENT ON COLUMN "public"."weather_alarm"."sms_status" IS '短信通知情况: 0、未发送短信；1、已发送预警短信；2、已发送预警解除短信';
    COMMENT ON TABLE "public"."weather_alarm" IS '天气预警信息表';
    COMMENT ON COLUMN "public"."weather_alarm"."sub_code" IS '子区域代码（逗号分隔）';
    COMMENT ON COLUMN "public"."weather_alarm"."sub_name" IS '子区域名称（逗号分隔）';
    """
    return create_table_sql

def setup_partition_management(engine):
    """
    设置 pg_partman 分区管理
    """
    print("\n开始配置分区管理...")

    # 检查 pg_partman 扩展是否存在
    try:
        with engine.begin() as conn:
            partman_exists = conn.execute(text("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_extension WHERE extname = 'pg_partman'
                )
            """)).scalar()

            if not partman_exists:
                print("⚠️ pg_partman 扩展未安装，跳过分区管理配置")
                print("   建议安装: CREATE EXTENSION pg_partman;")
                return

            print("✓ pg_partman 扩展已安装")

            # 检查 create_parent 函数的签名
            print("检查 pg_partman 函数签名...")
            try:
                func_info = conn.execute(text("""
                    SELECT
                        p.proname,
                        pg_get_function_arguments(p.oid) as args,
                        pg_get_function_result(p.oid) as result
                    FROM pg_proc p
                    JOIN pg_namespace n ON p.pronamespace = n.oid
                    WHERE n.nspname = 'partman' AND p.proname = 'create_parent'
                    ORDER BY p.oid
                """)).fetchall()

                print(f"找到 {len(func_info)} 个 create_parent 函数:")
                for i, (name, args, result) in enumerate(func_info):
                    print(f"  {i+1}. {name}({args}) -> {result}")

            except Exception as e:
                print(f"⚠️ 无法查询函数签名: {e}")

        # 核心数据表配置 (每日分区, 保留90天, 预创建7天)
        core_tables = [
            'public.weather_cell_6m',
            'public.weather_cell_1h',
            'public.weather_cell_obs',
            'public.forecast_precipitation_6min_line',
            'public.forecast_precipitation_6min_polygon',
            'public.forecast_precipitation_6min_relation',
            'public.forecast_precipitation_hourly_line',
            'public.forecast_precipitation_hourly_polygon',
            'public.forecast_precipitation_hourly_relation',
            'public.forecast_precipitation_summary_line',
            'public.forecast_precipitation_summary_polygon',
            'public.forecast_precipitation_summary_relation',
            'public.obs_precipitation_10min_line',
            'public.obs_precipitation_10min_polygon',
            'public.obs_precipitation_10min_relation',
            'public.obs_precipitation_1h_line',
            'public.obs_precipitation_1h_polygon',
            'public.obs_precipitation_1h_relation'
        ]

        print("配置核心数据表 (每日分区, 保留90天):")
        for table_name in core_tables:
            try:
                # 每个表使用独立的事务
                with engine.begin() as conn:
                    # 先检查是否已经配置了分区
                    existing_config = conn.execute(text("""
                        SELECT parent_table FROM partman.part_config
                        WHERE parent_table = :table_name
                    """), {"table_name": table_name}).fetchone()

                    if existing_config:
                        print(f"  ✓ {table_name} (已存在分区配置)")
                        # 更新保留策略和预创建分区
                        try:
                            conn.execute(text("""
                                UPDATE partman.part_config
                                SET retention = '90 days',
                                    retention_keep_table = false,
                                    premake = 7
                                WHERE parent_table = :table_name
                            """), {"table_name": table_name})
                            print(f"    ✓ 保留策略已更新")
                        except Exception as retention_error:
                            print(f"    ⚠️ 保留策略更新失败: {retention_error}")
                    else:
                        # 创建新的分区配置
                        result = conn.execute(text("""
                            SELECT partman.create_parent(
                                :table_name,
                                'pre_time',
                                '1 day'
                            )
                        """), {"table_name": table_name}).scalar()

                        if result:
                            print(f"  ✓ {table_name} (新建分区配置)")

                            # 设置保留策略和预创建分区
                            try:
                                conn.execute(text("""
                                    UPDATE partman.part_config
                                    SET retention = '90 days',
                                        retention_keep_table = false,
                                        premake = 7
                                    WHERE parent_table = :table_name
                                """), {"table_name": table_name})
                                print(f"    ✓ 保留策略已设置")
                            except Exception as retention_error:
                                print(f"    ⚠️ 保留策略设置失败: {retention_error}")
                        else:
                            print(f"  ⚠️ {table_name} (分区配置创建失败)")
            except Exception as e:
                print(f"  ❌ {table_name}: {e}")

        # 预警表配置 (每月分区, 保留3年, 预创建4个月)
        print("配置预警表 (每月分区, 保留3年):")
        try:
            with engine.begin() as conn:
                # 先检查是否已经配置了分区
                existing_config = conn.execute(text("""
                    SELECT parent_table FROM partman.part_config
                    WHERE parent_table = 'public.weather_alarm'
                """)).fetchone()

                if existing_config:
                    print("  ✓ public.weather_alarm (已存在分区配置)")
                    # 更新保留策略和预创建分区
                    try:
                        conn.execute(text("""
                            UPDATE partman.part_config
                            SET retention = '3 years',
                                retention_keep_table = false,
                                premake = 4
                            WHERE parent_table = 'public.weather_alarm'
                        """))
                        print("    ✓ 保留策略已更新")
                    except Exception as retention_error:
                        print(f"    ⚠️ 保留策略更新失败: {retention_error}")
                else:
                    # 创建新的分区配置
                    result = conn.execute(text("""
                        SELECT partman.create_parent(
                            'public.weather_alarm',
                            'publish_time',
                            '1 month'
                        )
                    """)).scalar()

                    if result:
                        print("  ✓ public.weather_alarm (新建分区配置)")

                        # 设置保留策略和预创建分区
                        try:
                            conn.execute(text("""
                                UPDATE partman.part_config
                                SET retention = '3 years',
                                    retention_keep_table = false,
                                    premake = 4
                                WHERE parent_table = 'public.weather_alarm'
                            """))
                            print("    ✓ 保留策略已设置")
                        except Exception as retention_error:
                            print(f"    ⚠️ 保留策略设置失败: {retention_error}")
                    else:
                        print("  ⚠️ public.weather_alarm (分区配置创建失败)")
        except Exception as e:
            print(f"  ❌ public.weather_alarm: {e}")

        # 设置自动维护任务（不依赖pg_cron）
        print("设置自动维护任务:")
        try:
            with engine.begin() as conn:
                # 直接调用分区维护过程
                print("  执行分区维护过程...")
                conn.execute(text("CALL partman.run_maintenance_proc()"))
                print("  ✓ 分区维护过程执行完成")

                print("  📋 定时维护任务设置说明:")
                print("     由于不依赖pg_cron扩展，请在系统级别设置定时任务")
                print("     建议使用以下方式之一:")
                print("     ")
                print("     1. Linux crontab:")
                print("        0 2 * * * psql -h <host> -p <port> -U <user> -d <database> -c \"CALL partman.run_maintenance_proc()\"")
                print("     ")
                print("     2. Windows 任务计划程序:")
                print("        创建每日凌晨2点执行的任务，运行命令:")
                print("        psql -h <host> -p <port> -U <user> -d <database> -c \"CALL partman.run_maintenance_proc()\"")
                print("     ")
                print("     3. 应用程序内置定时器:")
                print("        在主应用程序中添加每日定时执行 partman.run_maintenance_proc() 的逻辑")

        except Exception as e:
            print(f"  ❌ 执行维护任务失败: {e}")
            print("     请手动执行: CALL partman.run_maintenance_proc()")

        print("✓ 分区管理配置完成")

    except Exception as e:
        print(f"⚠️ 分区管理配置失败: {e}")
        print("   表创建成功，但分区管理需要手动配置")

def create_missing_partitions(engine, start_date=None, days=7):
    """
    手动创建缺失的分区

    Args:
        engine: 数据库引擎
        start_date: 开始日期，默认为今天
        days: 创建多少天的分区，默认7天
    """
    from datetime import datetime, timedelta

    if start_date is None:
        start_date = datetime.now().date()
    elif isinstance(start_date, str):
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

    print(f"\n开始创建缺失的分区 (从 {start_date} 开始，共 {days} 天)...")

    # 需要创建日分区的表
    daily_partition_tables = [
        'public.weather_cell_6m',
        'public.weather_cell_1h',
        'public.weather_cell_obs',
        'public.forecast_precipitation_6min_line',
        'public.forecast_precipitation_6min_polygon',
        'public.forecast_precipitation_6min_relation',
        'public.forecast_precipitation_hourly_line',
        'public.forecast_precipitation_hourly_polygon',
        'public.forecast_precipitation_hourly_relation',
        'public.forecast_precipitation_summary_line',
        'public.forecast_precipitation_summary_polygon',
        'public.forecast_precipitation_summary_relation',
        'public.obs_precipitation_10min_line',
        'public.obs_precipitation_10min_polygon',
        'public.obs_precipitation_10min_relation',
        'public.obs_precipitation_1h_line',
        'public.obs_precipitation_1h_polygon',
        'public.obs_precipitation_1h_relation'
    ]

    try:
        with engine.begin() as conn:
            processed_months = set()  # 跟踪已处理的月份，避免重复创建月分区

            for i in range(days):
                current_date = start_date + timedelta(days=i)
                next_date = current_date + timedelta(days=1)

                date_str = current_date.strftime('%Y%m%d')

                # 创建日分区
                for table_name in daily_partition_tables:
                    table_short_name = table_name.replace('public.', '')
                    partition_name = f"{table_short_name}_p{date_str}"

                    try:
                        # 检查分区是否已存在
                        exists = conn.execute(text("""
                            SELECT EXISTS (
                                SELECT 1 FROM pg_tables
                                WHERE schemaname = 'public'
                                AND tablename = :partition_name
                            )
                        """), {"partition_name": partition_name}).scalar()

                        if exists:
                            print(f"  ✓ {partition_name} (已存在)")
                            continue

                        # 创建分区
                        create_partition_sql = f"""
                            CREATE TABLE IF NOT EXISTS public.{partition_name}
                            PARTITION OF {table_name}
                            FOR VALUES FROM ('{current_date}') TO ('{next_date}')
                        """

                        conn.execute(text(create_partition_sql))
                        print(f"  ✓ {partition_name} (新建)")

                    except Exception as partition_error:
                        print(f"  ❌ {partition_name}: {partition_error}")
                        continue

                # 创建月分区 (weather_alarm表)
                year_month = current_date.strftime('%Y-%m')
                if year_month not in processed_months:
                    processed_months.add(year_month)

                    # 计算月份的开始和结束日期
                    month_start = current_date.replace(day=1)
                    if month_start.month == 12:
                        month_end = month_start.replace(year=month_start.year + 1, month=1)
                    else:
                        month_end = month_start.replace(month=month_start.month + 1)

                    month_str = current_date.strftime('%Y%m')
                    alarm_partition_name = f"weather_alarm_p{month_str}"

                    try:
                        # 检查月分区是否已存在
                        exists = conn.execute(text("""
                            SELECT EXISTS (
                                SELECT 1 FROM pg_tables
                                WHERE schemaname = 'public'
                                AND tablename = :partition_name
                            )
                        """), {"partition_name": alarm_partition_name}).scalar()

                        if exists:
                            print(f"  ✓ {alarm_partition_name} (已存在)")
                        else:
                            # 创建月分区
                            create_partition_sql = f"""
                                CREATE TABLE IF NOT EXISTS public.{alarm_partition_name}
                                PARTITION OF public.weather_alarm
                                FOR VALUES FROM ('{month_start}') TO ('{month_end}')
                            """

                            conn.execute(text(create_partition_sql))
                            print(f"  ✓ {alarm_partition_name} (新建)")

                    except Exception as partition_error:
                        print(f"  ❌ {alarm_partition_name}: {partition_error}")
                        continue

    except Exception as e:
        print(f"创建分区时发生错误: {e}")
        raise

def create_all_tables():
    """
    创建所有预报相关表并配置分区管理
    """
    engine = create_engine(PG_URL)

    tables = [
        ("weather_cell_6m", create_weather_cell_6m_table()),
        ("weather_cell_1h", create_weather_cell_1h_table()),
        ("weather_cell_obs", create_weather_cell_obs_table()),
        ("weather_alarm", create_weather_alarm_table()),
        ("forecast_precipitation_6min_line", create_forecast_precipitation_6min_line_table()),
        ("forecast_precipitation_6min_polygon", create_forecast_precipitation_6min_polygon_table()),
        ("forecast_precipitation_6min_relation", create_forecast_precipitation_6min_relation_table()),
        ("forecast_precipitation_hourly_line", create_forecast_precipitation_hourly_line_table()),
        ("forecast_precipitation_hourly_polygon", create_forecast_precipitation_hourly_polygon_table()),
        ("forecast_precipitation_hourly_relation", create_forecast_precipitation_hourly_relation_table()),
        ("forecast_precipitation_summary_line", create_forecast_precipitation_summary_line_table()),
        ("forecast_precipitation_summary_polygon", create_forecast_precipitation_summary_polygon_table()),
        ("forecast_precipitation_summary_relation", create_forecast_precipitation_summary_relation_table()),
        ("obs_precipitation_10min_line", create_obs_precipitation_10min_line_table()),
        ("obs_precipitation_10min_polygon", create_obs_precipitation_10min_polygon_table()),
        ("obs_precipitation_10min_relation", create_obs_precipitation_10min_relation_table()),
        ("obs_precipitation_1h_line", create_obs_precipitation_1h_line_table()),
        ("obs_precipitation_1h_polygon", create_obs_precipitation_1h_polygon_table()),
        ("obs_precipitation_1h_relation", create_obs_precipitation_1h_relation_table())
    ]

    try:
        for table_name, sql in tables:
            print(f"创建表: {table_name}")
            try:
                # 为每个表使用独立的事务
                with engine.begin() as conn:
                    conn.execute(text(sql))
                print(f"✓ {table_name} 表创建/检查完成")
            except Exception as table_error:
                print(f"⚠️ {table_name} 表创建时遇到问题: {table_error}")
                # 如果是字段不存在的错误，可能是表结构不匹配，跳过该表
                if "does not exist" in str(table_error):
                    print(f"   跳过 {table_name} 表（可能需要手动处理表结构）")
                    continue
                else:
                    print(f"   跳过 {table_name} 表，继续处理其他表")
                    continue

        print("\n✓ 所有预报表创建完成")

        # 创建PostgreSQL函数
        print("\n开始创建PostgreSQL函数...")
        try:
            print("创建函数: get_rainfall_type")
            with engine.begin() as conn:
                conn.execute(text(create_get_rainfall_type_function()))
            print("✓ get_rainfall_type 函数创建完成")
        except Exception as func_error:
            print(f"⚠️ get_rainfall_type 函数创建时遇到问题: {func_error}")
            print("   跳过函数创建，继续处理存储过程")

        try:
            print("创建函数: get_rainfall_type_hourly")
            with engine.begin() as conn:
                conn.execute(text(create_get_rainfall_type_hourly_function()))
            print("✓ get_rainfall_type_hourly 函数创建完成")
        except Exception as func_error:
            print(f"⚠️ get_rainfall_type_hourly 函数创建时遇到问题: {func_error}")
            print("   跳过函数创建，继续处理存储过程")

        try:
            print("创建函数: get_rainfall_type_obs_10m")
            with engine.begin() as conn:
                conn.execute(text(create_get_rainfall_type_obs_10m_function()))
            print("✓ get_rainfall_type_obs_10m 函数创建完成")
        except Exception as func_error:
            print(f"⚠️ get_rainfall_type_obs_10m 函数创建时遇到问题: {func_error}")
            print("   跳过函数创建，继续处理存储过程")

        try:
            print("创建函数: get_rainfall_type_obs_1h")
            with engine.begin() as conn:
                conn.execute(text(create_get_rainfall_type_obs_1h_function()))
            print("✓ get_rainfall_type_obs_1h 函数创建完成")
        except Exception as func_error:
            print(f"⚠️ get_rainfall_type_obs_1h 函数创建时遇到问题: {func_error}")
            print("   跳过函数创建，继续处理存储过程")

        # 创建存储过程
        procedures = [
            # 通用存储过程（推荐使用）
            ("sp_precip_polygon", create_sp_precip_polygon_procedure()),
            ("sp_precip_line", create_sp_precip_line_procedure())
        ]

        print("\n开始创建存储过程...")
        for proc_name, sql in procedures:
            print(f"创建存储过程: {proc_name}")
            try:
                with engine.begin() as conn:
                    conn.execute(text(sql))
                print(f"✓ {proc_name} 存储过程创建完成")
            except Exception as proc_error:
                print(f"⚠️ {proc_name} 存储过程创建时遇到问题: {proc_error}")
                print(f"   跳过 {proc_name} 存储过程，继续处理其他存储过程")
                continue

        print("\n✓ 所有函数和存储过程创建完成")

        # 配置分区管理
        setup_partition_management(engine)

        # 创建缺失的分区
        create_missing_partitions(engine)

    except Exception as e:
        print(f"创建表和存储过程时发生错误: {e}")
        raise
    finally:
        engine.dispose()

if __name__ == '__main__':
    print("开始创建降水预报相关数据表...")
    create_all_tables()