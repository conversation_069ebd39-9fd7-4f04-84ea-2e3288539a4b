#!/usr/bin/env python3
# coding: utf-8
"""
测试修复后的预警内容解析功能
"""

import sys
import os
import re
from typing import List

# 直接复制修复后的解析方法
def extract_admin_region_name(alarm_content: str) -> str:
    """提取行政区名称（气象台之前的内容）"""
    match = re.search(r'^(.+?)气象台', alarm_content)
    if match:
        return match.group(1).strip()
    return ""

def extract_content_after_colon(alarm_content: str) -> str:
    """提取第一个冒号后的内容"""
    # 先查找中文冒号
    chinese_colon_index = alarm_content.find('：')
    # 再查找英文冒号
    english_colon_index = alarm_content.find(':')

    # 选择最早出现的冒号
    colon_index = -1
    if chinese_colon_index != -1 and english_colon_index != -1:
        colon_index = min(chinese_colon_index, english_colon_index)
    elif chinese_colon_index != -1:
        colon_index = chinese_colon_index
    elif english_colon_index != -1:
        colon_index = english_colon_index

    if colon_index != -1:
        return alarm_content[colon_index + 1:].strip()
    return ""

def extract_time_and_location(content: str) -> str:
    """提取时间信息后的地点内容"""
    # 简化的匹配策略
    # 1. 先找到"天"或"小时"的位置
    time_patterns = [r'未来\d+(?:天|小时)', r'\d+(?:天|小时)', r'(?:天|小时)']

    for time_pattern in time_patterns:
        match = re.search(time_pattern, content)
        if match:
            # 获取时间词后的内容
            after_time = content[match.end():].strip()

            # 去除开头的标点符号和空格
            after_time = re.sub(r'^[，,。！？\s]+', '', after_time)

            # 查找地点信息的策略：
            # 1. 优先匹配包含多个地名的完整列表（如：县名+多个乡镇名）
            # 2. 匹配单个地名
            # 3. 匹配第一个逗号前的内容
            location_patterns = [
                # 匹配县/市名 + 多个乡镇名的模式（用、或，分隔）
                r'([^，,。！？]*?(?:县|市|区)[^，,。！？]*?(?:[、，,][^，,。！？]*?(?:镇|乡|街道|村|等))+[^，,。！？]*?)(?:[，,。！？]|$)',
                # 匹配包含地名标识的单个地点
                r'([^，,。！？]*?(?:县|市|镇|乡|区|街道|村|等)[^，,。！？]*?)(?:[，,。！？]|$)',
                # 匹配第一个逗号前的内容
                r'([^，,。！？]+?)(?:[，,。！？]|$)'
            ]

            for loc_pattern in location_patterns:
                loc_match = re.search(loc_pattern, after_time)
                if loc_match:
                    location_text = loc_match.group(1).strip()
                    if location_text:
                        return location_text

            break

    return ""

def clean_location_prefix(location: str, admin_region: str) -> str:
    """清理地点前缀"""
    # 去除常见前缀
    prefixes = ['我县', '我区', '我州', '我市']
    for prefix in prefixes:
        if location.startswith(prefix):
            location = location[len(prefix):].strip()
            break

    # 去除行政区名称前缀
    if admin_region and location.startswith(admin_region):
        location = location[len(admin_region):].strip()

    # 去除末尾的多余文字（如"将出现冰雹"、"等乡镇"等）
    suffixes_to_remove = [
        r'等乡镇.*$',  # 去除"等乡镇"及其后面的所有内容
        r'将出现.*$',  # 去除"将出现"及其后面的所有内容
        r'等地.*$',    # 去除"等地"及其后面的所有内容
        r'等.*$'       # 去除"等"及其后面的所有内容（最后匹配，避免过度匹配）
    ]

    for suffix_pattern in suffixes_to_remove:
        new_location = re.sub(suffix_pattern, '', location).strip()
        if new_location != location:
            location = new_location
            break

    return location

def split_locations(location_text: str) -> List[str]:
    """使用顿号分割地点"""
    # 使用顿号分割
    locations = re.split(r'[、]', location_text)
    return [loc.strip() for loc in locations if loc.strip()]

def test_alarm_parsing():
    """测试预警内容解析"""
    # 测试内容
    alarm_content = "武定县气象台2025年7月27日16时00分发布冰雹橙色预警信号:预计未来6小时武定县狮山、高桥、猫街、白路、环州、插甸、田心、东坡、发窝、万德、己衣等乡镇将出现冰雹，请做好防范。（预警信息来源：国家预警信息发布中心）"

    print("=" * 80)
    print("测试修复后的预警内容解析")
    print(f"原始内容: {alarm_content}")
    print("=" * 80)

    # 1. 提取行政区名称
    print("\n步骤1: 提取行政区名称")
    admin_region = extract_admin_region_name(alarm_content)
    print(f"行政区名称: '{admin_region}'")

    # 2. 提取冒号后的内容
    print("\n步骤2: 提取冒号后的内容")
    content_after_colon = extract_content_after_colon(alarm_content)
    print(f"冒号后内容: '{content_after_colon}'")

    # 3. 提取时间后的地点内容
    print("\n步骤3: 提取时间后的地点内容")
    location_text = extract_time_and_location(content_after_colon)
    print(f"地点内容: '{location_text}'")

    # 4. 清理地点前缀
    print("\n步骤4: 清理地点前缀")
    cleaned_location = clean_location_prefix(location_text, admin_region)
    print(f"清理后地点: '{cleaned_location}'")

    # 5. 分割地点
    print("\n步骤5: 分割地点")
    locations = split_locations(cleaned_location)
    print(f"分割后地点列表: {locations}")

    if locations:
        print("\n✅ 解析成功完成！")
        print(f"最终结果: {len(locations)} 个地点")
        for i, loc in enumerate(locations, 1):
            print(f"  {i}. {loc}")
    else:
        print("\n❌ 解析失败")

    # 测试其他预警内容格式
    print("\n" + "=" * 80)
    print("测试其他预警内容格式")
    print("=" * 80)

    test_cases = [
        "昆明市气象台2025年7月27日发布雷电黄色预警：预计未来2小时昆明市五华区、盘龙区、官渡区等地将出现雷电活动。",
        "大理州气象台发布暴雨橙色预警信号：预计未来3小时大理市、洱源县、剑川县等地将出现暴雨，请注意防范。",
        "红河州气象台发布大风蓝色预警：未来12小时个旧市、开远市、蒙自市、建水县等地将出现6-8级大风。"
    ]

    for i, test_content in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}:")
        print(f"内容: {test_content}")

        admin_region = extract_admin_region_name(test_content)
        content_after_colon = extract_content_after_colon(test_content)
        location_text = extract_time_and_location(content_after_colon)
        cleaned_location = clean_location_prefix(location_text, admin_region)
        locations = split_locations(cleaned_location)

        print(f"解析结果: {locations}")

if __name__ == "__main__":
    test_alarm_parsing()
